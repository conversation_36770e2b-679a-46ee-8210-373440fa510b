package com.zara.assistant.data.local.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

/**
 * Room entity for conversations
 */
@Entity(tableName = "conversations")
data class ConversationEntity(
    @PrimaryKey
    val id: String,
    val startTime: Date,
    val endTime: Date? = null,
    val isActive: Boolean = true,
    val summary: String? = null,
    val totalMessages: Int = 0,
    val averageResponseTime: Long = 0L
)

/**
 * Room entity for conversation messages
 */
@Entity(
    tableName = "conversation_messages",
    foreignKeys = [
        androidx.room.ForeignKey(
            entity = ConversationEntity::class,
            parentColumns = ["id"],
            childColumns = ["conversationId"],
            onDelete = androidx.room.ForeignKey.CASCADE
        )
    ],
    indices = [
        androidx.room.Index(value = ["conversationId"])
    ]
)
data class ConversationMessageEntity(
    @PrimaryKey
    val id: String,
    val conversationId: String,
    val content: String,
    val timestamp: Date,
    val sender: String, // USER, ZARA, SYSTEM
    val messageType: String = "TEXT", // TEXT, VOICE, ACTION, ERROR, SYSTEM_NOTIFICATION
    val metadata: String = "{}" // JSON string
)
