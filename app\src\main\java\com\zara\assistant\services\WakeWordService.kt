package com.zara.assistant.services

import ai.picovoice.porcupine.Porcupine
import ai.picovoice.porcupine.PorcupineException
import ai.picovoice.porcupine.PorcupineInvalidArgumentException
import ai.picovoice.porcupine.PorcupineManager
import ai.picovoice.porcupine.PorcupineManagerCallback
import android.app.Notification
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Binder
import android.os.IBinder
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import com.zara.assistant.BuildConfig
import com.zara.assistant.R
import com.zara.assistant.ZaraApplication
import com.zara.assistant.core.Constants
import com.zara.assistant.ui.MainActivity
import com.zara.assistant.utils.ApiKeyManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.cancel
import java.util.*
import javax.inject.Inject

/**
 * Foreground service for wake word detection using Picovoice Porcupine
 */
@AndroidEntryPoint
class WakeWordService : Service() {

    companion object {
        private const val TAG = "WakeWordService"
        private const val NOTIFICATION_ID = ZaraApplication.WAKE_WORD_NOTIFICATION_ID
        
        const val ACTION_START_DETECTION = "START_DETECTION"
        const val ACTION_STOP_DETECTION = "STOP_DETECTION"
        const val ACTION_WAKE_WORD_DETECTED = "WAKE_WORD_DETECTED"
        
        fun startService(context: Context) {
            val intent = Intent(context, WakeWordService::class.java).apply {
                action = ACTION_START_DETECTION
            }
            ContextCompat.startForegroundService(context, intent)
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, WakeWordService::class.java).apply {
                action = ACTION_STOP_DETECTION
            }
            context.startService(intent)
        }
    }

    private val binder = WakeWordBinder()
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    private var porcupineManager: PorcupineManager? = null
    private var isDetectionActive = false
    private var textToSpeech: TextToSpeech? = null

    private val _detectionState = MutableStateFlow(false)
    val detectionState: StateFlow<Boolean> = _detectionState.asStateFlow()
    
    private val _wakeWordDetected = MutableStateFlow<Long?>(null)
    val wakeWordDetected: StateFlow<Long?> = _wakeWordDetected.asStateFlow()

    @Inject
    lateinit var notificationManager: NotificationManager

    @Inject
    lateinit var apiKeyManager: ApiKeyManager

    inner class WakeWordBinder : Binder() {
        fun getService(): WakeWordService = this@WakeWordService
    }

    override fun onBind(intent: Intent?): IBinder = binder

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "WakeWordService created")
        initializeTTS()
    }

    private fun initializeTTS() {
        textToSpeech = TextToSpeech(this) { status ->
            if (status == TextToSpeech.SUCCESS) {
                textToSpeech?.language = Locale.getDefault()
                Log.d(TAG, "✅ TTS initialized successfully")
            } else {
                Log.e(TAG, "❌ TTS initialization failed")
            }
        }
    }

    private fun speakAcknowledgmentAndStartSTT() {
        try {
            val acknowledgments = listOf(
                "Yes sir",
                "Yes",
                "I'm here",
                "How can I help?",
                "Ready"
            )

            val randomAcknowledgment = acknowledgments.random()
            val utteranceId = "wake_ack_${System.currentTimeMillis()}"

            Log.d(TAG, "🗣️ Speaking acknowledgment: $randomAcknowledgment")

            // Set up TTS callback to start STT after acknowledgment finishes
            textToSpeech?.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
                override fun onStart(utteranceId: String?) {
                    Log.d(TAG, "🎤 Acknowledgment TTS started: $utteranceId")
                }

                override fun onDone(utteranceId: String?) {
                    Log.d(TAG, "✅ Acknowledgment TTS finished, starting STT")
                    startVoiceProcessingAfterAcknowledgment()
                }

                override fun onError(utteranceId: String?) {
                    Log.e(TAG, "❌ Acknowledgment TTS error, starting STT anyway")
                    startVoiceProcessingAfterAcknowledgment()
                }
            })

            textToSpeech?.speak(
                randomAcknowledgment,
                TextToSpeech.QUEUE_FLUSH,
                null,
                utteranceId
            )
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error speaking acknowledgment", e)
            // Fallback: start STT immediately if TTS fails
            startVoiceProcessingAfterAcknowledgment()
        }
    }

    private fun startVoiceProcessingAfterAcknowledgment() {
        try {
            Log.d(TAG, "🚀 Starting AdvancedVoiceProcessingService after acknowledgment...")
            AdvancedVoiceProcessingService.startListening(this@WakeWordService)
            Log.d(TAG, "✅ AdvancedVoiceProcessingService.startListening() called successfully")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start AdvancedVoiceProcessingService", e)
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_DETECTION -> startWakeWordDetection()
            ACTION_STOP_DETECTION -> stopWakeWordDetection()
        }
        return START_STICKY
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
        Log.d(TAG, "📱 App removed from recents, restarting wake word service")

        // Restart the service to keep wake word detection active
        val restartIntent = Intent(this, WakeWordService::class.java).apply {
            action = ACTION_START_DETECTION
        }
        startService(restartIntent)
    }

    private fun startWakeWordDetection() {
        if (isDetectionActive) {
            Log.d(TAG, "Wake word detection already active")
            return
        }

        if (!checkMicrophonePermission()) {
            Log.e(TAG, "Microphone permission not granted")
            stopSelf()
            return
        }

        try {
            startForeground(NOTIFICATION_ID, createNotification())
            initializePorcupine()
            isDetectionActive = true
            _detectionState.value = true
            Log.d(TAG, "Wake word detection started")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start wake word detection", e)
            stopSelf()
        }
    }

    private fun stopWakeWordDetection() {
        try {
            porcupineManager?.stop()
            porcupineManager?.delete()
            porcupineManager = null
            isDetectionActive = false
            _detectionState.value = false
            Log.d(TAG, "Wake word detection stopped")
            stopForeground(STOP_FOREGROUND_REMOVE)
            stopSelf()
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping wake word detection", e)
        }
    }

    private fun initializePorcupine() {
        try {
            // Use the API key from secure storage
            val accessKey = apiKeyManager.getPorcupineAccessKey()

            if (accessKey.isEmpty()) {
                Log.e(TAG, "Porcupine access key not configured")
                return
            }

            Log.d(TAG, "Initializing Porcupine with access key")

            val keywordPaths = arrayOf(
                // You would need to add the .ppn file for "Hey Zara" to assets
                "hey_zara_android.ppn"
            )

            val sensitivities = floatArrayOf(Constants.WakeWord.DEFAULT_SENSITIVITY)

            porcupineManager = PorcupineManager.Builder()
                .setAccessKey(accessKey)
                .setKeywordPaths(keywordPaths)
                .setSensitivities(sensitivities)
                .build(applicationContext, object : PorcupineManagerCallback {
                    override fun invoke(keywordIndex: Int) {
                        onWakeWordDetected(keywordIndex)
                    }
                })

            porcupineManager?.start()
            Log.d(TAG, "Porcupine initialized successfully")

        } catch (e: PorcupineException) {
            Log.e(TAG, "Failed to initialize Porcupine", e)
            throw e
        }
    }

    private fun onWakeWordDetected(keywordIndex: Int) {
        Log.d(TAG, "Wake word detected! Keyword index: $keywordIndex")

        serviceScope.launch {
            _wakeWordDetected.value = System.currentTimeMillis()

            // Broadcast wake word detection
            val intent = Intent(ACTION_WAKE_WORD_DETECTED).apply {
                putExtra("keyword_index", keywordIndex)
                putExtra("timestamp", System.currentTimeMillis())
            }
            sendBroadcast(intent)

            // Speak acknowledgment and start STT after TTS finishes
            speakAcknowledgmentAndStartSTT()
        }
    }

    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val stopIntent = Intent(this, WakeWordService::class.java).apply {
            action = ACTION_STOP_DETECTION
        }
        
        val stopPendingIntent = PendingIntent.getService(
            this, 1, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, ZaraApplication.WAKE_WORD_CHANNEL_ID)
            .setContentTitle(getString(R.string.wake_word_service_title))
            .setContentText(getString(R.string.wake_word_service_description))
            .setSmallIcon(R.drawable.ic_voice_assistant)
            .setContentIntent(pendingIntent)
            .addAction(
                R.drawable.ic_close,
                getString(R.string.stop),
                stopPendingIntent
            )
            .setOngoing(true)
            .setSilent(true)
            .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE)
            .build()
    }

    private fun checkMicrophonePermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            this,
            android.Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }

    fun updateSensitivity(sensitivity: Float) {
        if (isDetectionActive) {
            // Restart with new sensitivity
            stopWakeWordDetection()
            // Update sensitivity in preferences here
            startWakeWordDetection()
        }
    }

    fun isActive(): Boolean = isDetectionActive

    override fun onDestroy() {
        super.onDestroy()
        stopWakeWordDetection()
        textToSpeech?.shutdown()
        serviceScope.cancel()
        Log.d(TAG, "WakeWordService destroyed")
    }
}
