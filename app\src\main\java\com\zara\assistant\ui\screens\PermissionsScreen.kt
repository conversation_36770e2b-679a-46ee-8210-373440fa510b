package com.zara.assistant.ui.screens

import androidx.activity.ComponentActivity
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.zara.assistant.presentation.components.NeumorphismButton
import com.zara.assistant.presentation.components.NeumorphismCard
import com.zara.assistant.presentation.components.EnhancedNeumorphismButton
import com.zara.assistant.presentation.components.ErrorCard
import com.zara.assistant.presentation.components.ErrorType
import com.zara.assistant.presentation.components.LoadingWithError
import com.zara.assistant.utils.PermissionManager
import com.zara.assistant.ui.viewmodel.PermissionsViewModel

/**
 * Screen for requesting and managing permissions with graceful degradation
 */
@Composable
fun PermissionsScreen(
    onPermissionsGranted: () -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val activity = context as ComponentActivity
    val viewModel: PermissionsViewModel = hiltViewModel()
    val permissionManager = viewModel.permissionManager
    var permissionStatuses by remember { mutableStateOf(emptyList<PermissionManager.PermissionStatus>()) }
    var showOptionalPermissions by remember { mutableStateOf(false) }

    // Load permission statuses
    LaunchedEffect(Unit) {
        permissionStatuses = permissionManager.getPermissionStatuses()
    }

    // Check if core permissions are granted
    val corePermissionsGranted = permissionStatuses
        .filter { it.isRequired }
        .all { it.isGranted }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Header
        Text(
            text = "Permissions Required",
            style = MaterialTheme.typography.headlineLarge,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Zara needs these permissions to provide the best voice assistant experience.",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Core Permissions
        Text(
            text = "Required Permissions",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        // Show error if critical permissions are missing
        if (!corePermissionsGranted) {
            ErrorCard(
                title = "Permissions Required",
                message = "Zara needs these permissions to function properly. Some features may not work without them.",
                errorType = ErrorType.WARNING,
                modifier = Modifier.padding(bottom = 16.dp),
                autoHide = false
            )
        }

        LazyColumn(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Core permissions
            items(permissionStatuses.filter { it.isRequired }) { status ->
                PermissionStatusCard(
                    status = status,
                    permissionManager = permissionManager,
                    onRequestPermission = { permission ->
                        if (status.requiresSpecialHandling) {
                            permissionManager.openSpecialPermissionSettings(activity, permission)
                        } else {
                            permissionManager.requestCorePermissions(activity, 100)
                        }
                    }
                )
            }

            // Optional permissions toggle
            item {
                EnhancedNeumorphismButton(
                    text = if (showOptionalPermissions) "Hide Optional Permissions" else "Show Optional Permissions",
                    onClick = { showOptionalPermissions = !showOptionalPermissions },
                    modifier = Modifier.fillMaxWidth(),
                    isSecondary = true
                )
            }

            // Optional permissions
            if (showOptionalPermissions) {
                items(permissionStatuses.filter { !it.isRequired }) { status ->
                    PermissionStatusCard(
                        status = status,
                        permissionManager = permissionManager,
                        onRequestPermission = { permission ->
                            if (status.requiresSpecialHandling) {
                                permissionManager.openSpecialPermissionSettings(activity, permission)
                            } else {
                                permissionManager.requestOptionalPermissions(activity, 101)
                            }
                        }
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Action buttons
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (corePermissionsGranted) {
                EnhancedNeumorphismButton(
                    text = "Continue",
                    onClick = onPermissionsGranted,
                    modifier = Modifier.fillMaxWidth(),
                    icon = {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                )
            } else {
                EnhancedNeumorphismButton(
                    text = "Grant Required Permissions",
                    onClick = {
                        permissionManager.requestCorePermissions(activity, 100)
                    },
                    modifier = Modifier.fillMaxWidth(),
                    icon = {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            EnhancedNeumorphismButton(
                text = "Skip for Now",
                onClick = onNavigateBack,
                modifier = Modifier.fillMaxWidth(),
                isSecondary = true
            )
        }
    }
}

@Composable
private fun PermissionItem(
    permissionInfo: PermissionInfo,
    isGranted: Boolean,
    onRequestPermission: () -> Unit,
    modifier: Modifier = Modifier
) {
    NeumorphismCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = permissionInfo.title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    
                    if (permissionInfo.isRequired) {
                        Text(
                            text = " *",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = permissionInfo.description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Icon(
                imageVector = if (isGranted) Icons.Default.Check else Icons.Default.Close,
                contentDescription = if (isGranted) "Granted" else "Not granted",
                tint = if (isGranted) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.error
                },
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

/**
 * Data class for permission information
 */
private data class PermissionInfo(
    val permission: String,
    val title: String,
    val description: String,
    val isRequired: Boolean
)

/**
 * Composable for individual permission status card
 */
@Composable
private fun PermissionStatusCard(
    status: PermissionManager.PermissionStatus,
    permissionManager: PermissionManager,
    onRequestPermission: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    NeumorphismCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Permission status icon
            Icon(
                imageVector = if (status.isGranted) Icons.Default.Check else Icons.Default.Close,
                contentDescription = null,
                tint = if (status.isGranted)
                    MaterialTheme.colorScheme.primary
                else
                    MaterialTheme.colorScheme.error,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            // Permission details
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = permissionManager.getPermissionDisplayName(status.permission),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )

                Text(
                    text = permissionManager.getPermissionDescription(status.permission),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                if (status.isRequired) {
                    Text(
                        text = "Required",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Bold
                    )
                }

                if (status.requiresSpecialHandling) {
                    Text(
                        text = "Requires Settings",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.secondary,
                        fontWeight = FontWeight.Bold
                    )
                }
            }

            // Action button
            if (!status.isGranted) {
                Button(
                    onClick = { onRequestPermission(status.permission) },
                    modifier = Modifier.padding(start = 8.dp)
                ) {
                    Icon(
                        imageVector = if (status.requiresSpecialHandling) Icons.Default.Settings else Icons.Default.Check,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = if (status.requiresSpecialHandling) "Settings" else "Grant",
                        style = MaterialTheme.typography.labelMedium
                    )
                }
            }
        }
    }
}
