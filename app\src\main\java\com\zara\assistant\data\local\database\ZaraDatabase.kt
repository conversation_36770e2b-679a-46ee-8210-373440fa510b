package com.zara.assistant.data.local.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import com.zara.assistant.data.local.dao.CommandDao
import com.zara.assistant.data.local.dao.ConversationDao
import com.zara.assistant.data.local.dao.SettingsDao
import com.zara.assistant.data.local.database.entities.ConversationEntity
import com.zara.assistant.data.local.database.entities.ConversationMessageEntity
import com.zara.assistant.data.local.database.entities.SettingsEntity
import com.zara.assistant.data.local.database.entities.VoiceCommandEntity
import com.zara.assistant.domain.model.*

/**
 * Room database for Zara app
 */
@Database(
    entities = [
        ConversationEntity::class,
        ConversationMessageEntity::class,
        SettingsEntity::class,
        VoiceCommandEntity::class,
        // New learning system entities
        UserProfile::class,
        UserInteraction::class,
        UserPattern::class,
        UserPreference::class,
        ConversationHistory::class,
        SearchCache::class,
        UserFavorite::class,
        BehavioralPattern::class,
        MLModelData::class,
        ContextualData::class
    ],
    version = 3,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class ZaraDatabase : RoomDatabase() {

    abstract fun conversationDao(): ConversationDao
    abstract fun settingsDao(): SettingsDao
    abstract fun commandDao(): CommandDao
    abstract fun userLearningDao(): com.zara.assistant.data.local.dao.UserLearningDao
    
    companion object {
        @Volatile
        private var INSTANCE: ZaraDatabase? = null

        // Migration from version 2 to 3 - adds index to conversation_messages
        private val MIGRATION_2_3 = object : Migration(2, 3) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Add index for conversationId foreign key if it doesn't exist
                database.execSQL("CREATE INDEX IF NOT EXISTS index_conversation_messages_conversationId ON conversation_messages(conversationId)")
            }
        }

        fun getDatabase(context: Context): ZaraDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    ZaraDatabase::class.java,
                    "zara_database"
                )
                .addMigrations(MIGRATION_2_3)
                .fallbackToDestructiveMigration() // For development - remove in production
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
