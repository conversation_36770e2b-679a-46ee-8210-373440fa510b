<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Audio and Speech Permissions -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    
    <!-- Network Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- System Control Permissions -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />

    <!-- WiFi Control Permissions -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />

    <!-- Bluetooth Control Permissions -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <!-- Removed BLUETOOTH_PRIVILEGED - not available on standard devices -->

    <!-- Mobile Data and Network Control -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <!-- Removed CONNECTIVITY_INTERNAL - protected permission -->
    <!-- Removed TETHER_PRIVILEGED - protected permission -->

    <!-- NFC Control -->
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.NFC_PREFERRED_PAYMENT_INFO" />

    <!-- Location Services Control -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!-- Device Control Permissions -->
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
    <!-- Removed DEVICE_POWER - protected permission -->
    <!-- Removed REBOOT - protected permission -->

    <!-- Screen and Display Control -->
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />

    <!-- Telephony Control -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
    <!-- Removed MODIFY_PHONE_STATE - protected permission -->

    <!-- App Management Permissions -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <!-- PACKAGE_USAGE_STATS requires special permission through Settings -->

    <!-- System Settings Access -->
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <!-- Removed WRITE_SECURE_SETTINGS - protected permission -->
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    
    <!-- Notification Permissions -->
    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    
    <!-- Accessibility Permission -->
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
    
    <!-- Phone and Device Control -->
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    
    <!-- Storage Permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    
    <!-- Device Admin Permissions -->
    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />

    <!-- Removed protected permissions that are not available on standard devices:
         - INTERACT_ACROSS_USERS
         - MANAGE_USERS
         - STATUS_BAR
         - ACCESS_SUPERUSER
         - HARDWARE_TEST
         - DEVICE_POWER (duplicate)
         - WRITE_SECURE_SETTINGS (duplicate)
    -->

    <application
        android:name=".ZaraApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Zara"
        android:usesCleartextTraffic="false"
        tools:targetApi="31">

        <!-- Main Activity -->
        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.Zara"
            android:screenOrientation="portrait"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VOICE_COMMAND" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- Restricted Settings Guide Activity -->
        <activity
            android:name=".ui.RestrictedSettingsGuideActivity"
            android:exported="false"
            android:theme="@style/Theme.Zara"
            android:screenOrientation="portrait" />

        <!-- Wake Word Detection Service -->
        <service
            android:name=".services.WakeWordService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="microphone" />

        <!-- Advanced Voice Processing Service -->
        <service
            android:name=".services.AdvancedVoiceProcessingService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="microphone" />

        <!-- Accessibility Service for System Control -->
        <service
            android:name=".services.ZaraAccessibilityService"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            android:exported="true">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- Device Admin Receiver for Enhanced System Control -->
        <receiver
            android:name=".services.ZaraDeviceAdminReceiver"
            android:permission="android.permission.BIND_DEVICE_ADMIN"
            android:exported="true">
            <meta-data
                android:name="android.app.device_admin"
                android:resource="@xml/device_admin_config" />
            <intent-filter>
                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
                <action android:name="android.app.action.DEVICE_ADMIN_DISABLED" />
            </intent-filter>
        </receiver>

        <!-- Notification Listener Service -->
        <service
            android:name=".services.NotificationListenerService"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
            android:exported="true">
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>

        <!-- AI Processing Service -->
        <service
            android:name=".services.AIProcessingService"
            android:enabled="true"
            android:exported="false" />

        <!-- User Learning Service -->
        <service
            android:name=".services.UserLearningService"
            android:enabled="true"
            android:exported="false" />

        <!-- Work Manager for Background Tasks -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
        </provider>

    </application>

</manifest>
