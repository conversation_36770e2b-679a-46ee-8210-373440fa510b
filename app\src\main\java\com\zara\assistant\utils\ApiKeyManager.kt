package com.zara.assistant.utils

import android.content.Context
import android.content.SharedPreferences
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import android.util.Log
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec

/**
 * Secure API key manager using Android Keystore and EncryptedSharedPreferences
 * Replaces the insecure assets-based approach
 */
class ApiKeyManager(private val context: Context) {

    companion object {
        private const val TAG = "ApiKeyManager"
        private const val KEYSTORE_ALIAS = "ZaraApiKeysAlias"
        private const val ENCRYPTED_PREFS_NAME = "zara_encrypted_api_keys"
        private const val ANDROID_KEYSTORE = "AndroidKeyStore"

        // Key names for encrypted storage
        private const val KEY_COHERE = "cohere_api_key"
        private const val KEY_PERPLEXITY = "perplexity_api_key"
        private const val KEY_PORCUPINE = "porcupine_access_key"
        private const val KEY_AZURE_SPEECH = "azure_speech_key"
        private const val KEY_AZURE_REGION = "azure_speech_region"
        private const val KEY_OPENAI = "openai_api_key"
    }

    private var encryptedPrefs: SharedPreferences? = null
    private var isInitialized = false

    /**
     * Initialize secure API key storage
     */
    fun initialize() {
        try {
            // Create master key for encryption
            val masterKey = MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build()

            // Create encrypted shared preferences
            encryptedPrefs = EncryptedSharedPreferences.create(
                context,
                ENCRYPTED_PREFS_NAME,
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )

            isInitialized = true
            Log.d(TAG, "Secure API key storage initialized successfully")

            // Check if we need to migrate from old system
            migrateLegacyKeys()

        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize secure API key storage", e)
            isInitialized = false
        }
    }

    /**
     * Migrate from legacy API key storage (if exists)
     */
    private fun migrateLegacyKeys() {
        try {
            // Check if we have keys in local.properties or other legacy sources
            // This is a one-time migration - in production, keys should be set via secure setup
            Log.d(TAG, "Checking for legacy API keys to migrate...")

            // For now, we'll require manual setup of API keys through the app UI
            // This prevents accidental exposure of keys in code

        } catch (e: Exception) {
            Log.e(TAG, "Error during legacy key migration", e)
        }
    }

    /**
     * Store an API key securely
     */
    fun storeApiKey(keyName: String, keyValue: String): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "ApiKeyManager not initialized")
            return false
        }

        return try {
            encryptedPrefs?.edit()?.putString(keyName, keyValue)?.apply()
            Log.d(TAG, "API key '$keyName' stored securely")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to store API key '$keyName'", e)
            false
        }
    }

    /**
     * Retrieve an API key securely
     */
    private fun getSecureApiKey(keyName: String): String? {
        if (!isInitialized) {
            Log.e(TAG, "ApiKeyManager not initialized")
            return null
        }

        return try {
            val key = encryptedPrefs?.getString(keyName, null)
            if (key.isNullOrEmpty()) {
                Log.w(TAG, "API key '$keyName' not found or empty")
                return null
            }
            key
        } catch (e: Exception) {
            Log.e(TAG, "Failed to retrieve API key '$keyName'", e)
            null
        }
    }

    /**
     * Get Cohere API key
     */
    fun getCohereApiKey(): String {
        return getSecureApiKey(KEY_COHERE) ?: ""
    }

    /**
     * Get Perplexity API key
     */
    fun getPerplexityApiKey(): String {
        return getSecureApiKey(KEY_PERPLEXITY) ?: ""
    }

    /**
     * Get Porcupine access key
     */
    fun getPorcupineAccessKey(): String {
        return getSecureApiKey(KEY_PORCUPINE) ?: ""
    }

    /**
     * Get Azure Speech API key
     */
    fun getAzureSpeechKey(): String {
        return getSecureApiKey(KEY_AZURE_SPEECH) ?: ""
    }

    /**
     * Get Azure Speech region
     */
    fun getAzureSpeechRegion(): String {
        return getSecureApiKey(KEY_AZURE_REGION) ?: "eastus"
    }

    /**
     * Get OpenAI API key (optional)
     */
    fun getOpenAiApiKey(): String {
        return getSecureApiKey(KEY_OPENAI) ?: ""
    }

    /**
     * Store Cohere API key
     */
    fun storeCohereApiKey(key: String): Boolean {
        return storeApiKey(KEY_COHERE, key)
    }

    /**
     * Store Perplexity API key
     */
    fun storePerplexityApiKey(key: String): Boolean {
        return storeApiKey(KEY_PERPLEXITY, key)
    }

    /**
     * Store Porcupine access key
     */
    fun storePorcupineAccessKey(key: String): Boolean {
        return storeApiKey(KEY_PORCUPINE, key)
    }

    /**
     * Store Azure Speech API key
     */
    fun storeAzureSpeechKey(key: String): Boolean {
        return storeApiKey(KEY_AZURE_SPEECH, key)
    }

    /**
     * Store Azure Speech region
     */
    fun storeAzureSpeechRegion(region: String): Boolean {
        return storeApiKey(KEY_AZURE_REGION, region)
    }

    /**
     * Store OpenAI API key
     */
    fun storeOpenAiApiKey(key: String): Boolean {
        return storeApiKey(KEY_OPENAI, key)
    }

    /**
     * Check if all required API keys are configured
     */
    fun areApiKeysConfigured(): Boolean {
        if (!isInitialized) return false

        val cohereKey = getCohereApiKey()
        val perplexityKey = getPerplexityApiKey()
        val porcupineKey = getPorcupineAccessKey()
        val azureSpeechKey = getAzureSpeechKey()
        val azureSpeechRegion = getAzureSpeechRegion()

        return cohereKey.isNotEmpty() && perplexityKey.isNotEmpty() &&
               porcupineKey.isNotEmpty() && azureSpeechKey.isNotEmpty() &&
               azureSpeechRegion.isNotEmpty()
    }

    /**
     * Get list of missing API keys
     */
    fun getMissingApiKeys(): List<String> {
        val missing = mutableListOf<String>()

        if (getCohereApiKey().isEmpty()) missing.add("Cohere API Key")
        if (getPerplexityApiKey().isEmpty()) missing.add("Perplexity API Key")
        if (getPorcupineAccessKey().isEmpty()) missing.add("Porcupine Access Key")
        if (getAzureSpeechKey().isEmpty()) missing.add("Azure Speech API Key")
        if (getAzureSpeechRegion().isEmpty()) missing.add("Azure Speech Region")

        return missing
    }

    /**
     * Clear all stored API keys (for security purposes)
     */
    fun clearAllApiKeys(): Boolean {
        if (!isInitialized) return false

        return try {
            encryptedPrefs?.edit()?.clear()?.apply()
            Log.d(TAG, "All API keys cleared")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear API keys", e)
            false
        }
    }

    /**
     * Check if API key manager is properly initialized
     */
    fun isInitialized(): Boolean = isInitialized
}
