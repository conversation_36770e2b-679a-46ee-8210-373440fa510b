package com.zara.assistant.presentation.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.zara.assistant.core.Constants
import com.zara.assistant.presentation.theme.*
import kotlin.math.*

/**
 * Enhanced voice visualization with smooth animations and glow effects
 */
@Composable
fun EnhancedVoiceVisualization(
    voiceState: Constants.VoiceState,
    audioLevel: Float = 0f,
    modifier: Modifier = Modifier,
    style: VisualizationStyle = VisualizationStyle.WAVE_BARS
) {
    when (style) {
        VisualizationStyle.WAVE_BARS -> WaveBarVisualization(voiceState, audioLevel, modifier)
        VisualizationStyle.CIRCULAR_PULSE -> CircularPulseVisualization(voiceState, audioLevel, modifier)
        VisualizationStyle.SPECTRUM_ANALYZER -> SpectrumAnalyzerVisualization(voiceState, audioLevel, modifier)
    }
}

enum class VisualizationStyle {
    WAVE_BARS,
    CIRCULAR_PULSE,
    SPECTRUM_ANALYZER
}

@Composable
private fun WaveBarVisualization(
    voiceState: Constants.VoiceState,
    audioLevel: Float,
    modifier: Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "wave_bars")
    
    val targetColor = when (voiceState) {
        Constants.VoiceState.LISTENING_WAKE_WORD,
        Constants.VoiceState.LISTENING_COMMAND -> VoiceListening
        Constants.VoiceState.PROCESSING -> VoiceProcessing
        Constants.VoiceState.SPEAKING -> VoiceSpeaking
        else -> Color.Gray.copy(alpha = 0.3f)
    }
    
    val animatedColor by animateColorAsState(
        targetValue = targetColor,
        animationSpec = tween(durationMillis = 300, easing = FastOutSlowInEasing),
        label = "voiceColor"
    )
    
    val isActive = voiceState != Constants.VoiceState.IDLE
    
    // Wave animation
    val wavePhase by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2f * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "wavePhase"
    )
    
    // Intensity animation
    val intensity by infiniteTransition.animateFloat(
        initialValue = 0.2f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "intensity"
    )
    
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(80.dp)
    ) {
        if (isActive) {
            drawWaveBars(
                color = animatedColor,
                wavePhase = wavePhase,
                intensity = intensity,
                audioLevel = audioLevel
            )
        } else {
            drawIdleBars(color = animatedColor.copy(alpha = 0.3f))
        }
    }
}

@Composable
private fun CircularPulseVisualization(
    voiceState: Constants.VoiceState,
    audioLevel: Float,
    modifier: Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "circular_pulse")
    
    val targetColor = when (voiceState) {
        Constants.VoiceState.LISTENING_WAKE_WORD,
        Constants.VoiceState.LISTENING_COMMAND -> VoiceListening
        Constants.VoiceState.PROCESSING -> VoiceProcessing
        Constants.VoiceState.SPEAKING -> VoiceSpeaking
        else -> Color.Gray.copy(alpha = 0.3f)
    }
    
    val animatedColor by animateColorAsState(
        targetValue = targetColor,
        animationSpec = tween(durationMillis = 300),
        label = "pulseColor"
    )
    
    val isActive = voiceState != Constants.VoiceState.IDLE
    
    // Pulse animation
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulseScale"
    )
    
    // Rotation animation
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    Box(
        modifier = modifier.size(120.dp),
        contentAlignment = Alignment.Center
    ) {
        // Outer glow ring
        if (isActive) {
            Box(
                modifier = Modifier
                    .size((80 * pulseScale).dp)
                    .blur(8.dp)
                    .background(
                        color = animatedColor.copy(alpha = 0.3f),
                        shape = CircleShape
                    )
            )
        }
        
        // Main pulse circle
        Canvas(
            modifier = Modifier.size(80.dp)
        ) {
            if (isActive) {
                drawCircularPulse(
                    color = animatedColor,
                    scale = pulseScale,
                    rotation = rotation,
                    audioLevel = audioLevel
                )
            } else {
                drawCircle(
                    color = animatedColor.copy(alpha = 0.3f),
                    radius = size.minDimension / 4
                )
            }
        }
    }
}

@Composable
private fun SpectrumAnalyzerVisualization(
    voiceState: Constants.VoiceState,
    audioLevel: Float,
    modifier: Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "spectrum")
    
    val targetColor = when (voiceState) {
        Constants.VoiceState.LISTENING_WAKE_WORD,
        Constants.VoiceState.LISTENING_COMMAND -> VoiceListening
        Constants.VoiceState.PROCESSING -> VoiceProcessing
        Constants.VoiceState.SPEAKING -> VoiceSpeaking
        else -> Color.Gray.copy(alpha = 0.3f)
    }
    
    val animatedColor by animateColorAsState(
        targetValue = targetColor,
        animationSpec = tween(durationMillis = 300),
        label = "spectrumColor"
    )
    
    val isActive = voiceState != Constants.VoiceState.IDLE
    
    // Frequency animation
    val frequencyPhase by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2f * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1200, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "frequencyPhase"
    )
    
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(100.dp)
            .clip(RoundedCornerShape(8.dp))
    ) {
        if (isActive) {
            drawSpectrumBars(
                color = animatedColor,
                frequencyPhase = frequencyPhase,
                audioLevel = audioLevel
            )
        } else {
            drawIdleSpectrum(color = animatedColor.copy(alpha = 0.3f))
        }
    }
}

// Drawing functions
private fun DrawScope.drawWaveBars(
    color: Color,
    wavePhase: Float,
    intensity: Float,
    audioLevel: Float
) {
    val barCount = 25
    val barWidth = size.width / (barCount * 1.5f)
    val maxHeight = size.height * 0.8f
    val minHeight = size.height * 0.1f
    
    repeat(barCount) { index ->
        val x = (index + 0.5f) * (size.width / barCount)
        val normalizedIndex = index.toFloat() / (barCount - 1)
        val phaseOffset = normalizedIndex * PI.toFloat() * 2
        
        val waveValue = sin(wavePhase + phaseOffset)
        val heightMultiplier = 0.3f + 0.7f * intensity * (0.5f + 0.5f * waveValue)
        val barHeight = minHeight + (maxHeight - minHeight) * heightMultiplier
        
        val gradient = Brush.verticalGradient(
            colors = listOf(
                color.copy(alpha = 0.8f),
                color.copy(alpha = 0.4f)
            )
        )
        
        drawRect(
            brush = gradient,
            topLeft = Offset(x - barWidth / 2, size.height - barHeight),
            size = androidx.compose.ui.geometry.Size(barWidth, barHeight)
        )
    }
}

private fun DrawScope.drawIdleBars(color: Color) {
    val barCount = 25
    val barWidth = size.width / (barCount * 1.5f)
    val barHeight = size.height * 0.1f
    
    repeat(barCount) { index ->
        val x = (index + 0.5f) * (size.width / barCount)
        drawRect(
            color = color,
            topLeft = Offset(x - barWidth / 2, size.height - barHeight),
            size = androidx.compose.ui.geometry.Size(barWidth, barHeight)
        )
    }
}

private fun DrawScope.drawCircularPulse(
    color: Color,
    scale: Float,
    rotation: Float,
    audioLevel: Float
) {
    val center = Offset(size.width / 2, size.height / 2)
    val baseRadius = size.minDimension / 4
    val radius = baseRadius * scale
    
    // Draw multiple concentric circles for depth
    repeat(3) { ring ->
        val ringRadius = radius * (1f - ring * 0.2f)
        val alpha = 0.8f - ring * 0.2f
        
        drawCircle(
            color = color.copy(alpha = alpha),
            radius = ringRadius,
            center = center
        )
    }
    
    // Draw rotating elements
    repeat(8) { index ->
        val angle = rotation + index * 45f
        val elementRadius = radius * 1.2f
        val elementX = center.x + cos(angle * PI / 180) * elementRadius
        val elementY = center.y + sin(angle * PI / 180) * elementRadius
        
        drawCircle(
            color = color.copy(alpha = 0.6f),
            radius = 4.dp.toPx(),
            center = Offset(elementX.toFloat(), elementY.toFloat())
        )
    }
}

private fun DrawScope.drawSpectrumBars(
    color: Color,
    frequencyPhase: Float,
    audioLevel: Float
) {
    val barCount = 40
    val barWidth = size.width / (barCount * 1.2f)
    val maxHeight = size.height * 0.9f
    
    repeat(barCount) { index ->
        val x = (index + 0.5f) * (size.width / barCount)
        val frequency = index.toFloat() / barCount
        
        // Simulate frequency response
        val frequencyResponse = sin(frequencyPhase + frequency * PI.toFloat() * 4)
        val heightMultiplier = 0.1f + 0.9f * abs(frequencyResponse)
        val barHeight = maxHeight * heightMultiplier
        
        val gradient = Brush.verticalGradient(
            colors = listOf(
                color,
                color.copy(alpha = 0.3f)
            )
        )
        
        drawRect(
            brush = gradient,
            topLeft = Offset(x - barWidth / 2, size.height - barHeight),
            size = androidx.compose.ui.geometry.Size(barWidth, barHeight)
        )
    }
}

private fun DrawScope.drawIdleSpectrum(color: Color) {
    val barCount = 40
    val barWidth = size.width / (barCount * 1.2f)
    val barHeight = size.height * 0.05f
    
    repeat(barCount) { index ->
        val x = (index + 0.5f) * (size.width / barCount)
        drawRect(
            color = color,
            topLeft = Offset(x - barWidth / 2, size.height - barHeight),
            size = androidx.compose.ui.geometry.Size(barWidth, barHeight)
        )
    }
}
