package com.zara.assistant.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.zara.assistant.presentation.components.NeumorphismCard
import com.zara.assistant.presentation.components.EnhancedNeumorphismButton
import com.zara.assistant.presentation.components.ErrorCard
import com.zara.assistant.presentation.components.ErrorType
import com.zara.assistant.presentation.components.NeumorphismLoadingIndicator
import com.zara.assistant.ui.viewmodel.ApiKeySetupViewModel

/**
 * Screen for secure API key setup
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ApiKeySetupScreen(
    onSetupComplete: () -> Unit,
    viewModel: ApiKeySetupViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Header
        Text(
            text = "API Key Setup",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Text(
            text = "Enter your API keys to enable Zara's AI capabilities. Keys are stored securely using Android Keystore.",
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        // API Key Input Cards
        NeumorphismCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Cohere API Key",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                ApiKeyTextField(
                    value = uiState.cohereApiKey,
                    onValueChange = viewModel::updateCohereApiKey,
                    placeholder = "Enter your Cohere API key",
                    isError = uiState.cohereApiKeyError != null
                )
                
                uiState.cohereApiKeyError?.let { error ->
                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
        }
        
        NeumorphismCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Perplexity API Key",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                ApiKeyTextField(
                    value = uiState.perplexityApiKey,
                    onValueChange = viewModel::updatePerplexityApiKey,
                    placeholder = "Enter your Perplexity API key",
                    isError = uiState.perplexityApiKeyError != null
                )
                
                uiState.perplexityApiKeyError?.let { error ->
                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
        }
        
        NeumorphismCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Porcupine Access Key",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                ApiKeyTextField(
                    value = uiState.porcupineAccessKey,
                    onValueChange = viewModel::updatePorcupineAccessKey,
                    placeholder = "Enter your Porcupine access key",
                    isError = uiState.porcupineAccessKeyError != null
                )
                
                uiState.porcupineAccessKeyError?.let { error ->
                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
        }
        
        NeumorphismCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Azure Speech Services",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                ApiKeyTextField(
                    value = uiState.azureSpeechKey,
                    onValueChange = viewModel::updateAzureSpeechKey,
                    placeholder = "Enter your Azure Speech API key",
                    isError = uiState.azureSpeechKeyError != null
                )
                
                uiState.azureSpeechKeyError?.let { error ->
                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = uiState.azureSpeechRegion,
                    onValueChange = viewModel::updateAzureSpeechRegion,
                    label = { Text("Azure Region") },
                    placeholder = { Text("e.g., eastus") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    isError = uiState.azureSpeechRegionError != null
                )
                
                uiState.azureSpeechRegionError?.let { error ->
                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
        }
        
        // Save Button
        EnhancedNeumorphismButton(
            text = "Save API Keys",
            onClick = {
                viewModel.saveApiKeys(onSetupComplete)
            },
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp),
            enabled = !uiState.isLoading,
            icon = if (uiState.isLoading) {
                { NeumorphismLoadingIndicator(size = 16.dp) }
            } else null
        )

        // Skip Button (for testing)
        EnhancedNeumorphismButton(
            text = "Skip for now",
            onClick = onSetupComplete,
            modifier = Modifier.padding(top = 8.dp),
            isSecondary = true
        )
        
        // Error Message
        uiState.errorMessage?.let { error ->
            ErrorCard(
                title = "Setup Error",
                message = error,
                errorType = ErrorType.ERROR,
                modifier = Modifier.padding(top = 16.dp),
                onRetry = { viewModel.clearErrorMessage() },
                autoHide = false
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ApiKeyTextField(
    value: String,
    onValueChange: (String) -> Unit,
    placeholder: String,
    isError: Boolean = false
) {
    var isVisible by remember { mutableStateOf(false) }
    
    OutlinedTextField(
        value = value,
        onValueChange = onValueChange,
        label = { Text(placeholder) },
        modifier = Modifier.fillMaxWidth(),
        singleLine = true,
        visualTransformation = if (isVisible) VisualTransformation.None else PasswordVisualTransformation(),
        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
        trailingIcon = {
            IconButton(onClick = { isVisible = !isVisible }) {
                Icon(
                    imageVector = if (isVisible) Icons.Default.Visibility else Icons.Default.VisibilityOff,
                    contentDescription = if (isVisible) "Hide API key" else "Show API key"
                )
            }
        },
        isError = isError
    )
}
