package com.zara.assistant.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.zara.assistant.ui.screens.AboutScreen
import com.zara.assistant.ui.screens.ApiKeySetupScreen
import com.zara.assistant.ui.screens.CommandsHelpScreen
import com.zara.assistant.ui.screens.MainScreen
import com.zara.assistant.ui.screens.OnboardingScreen
import com.zara.assistant.ui.screens.PermissionsScreen
import com.zara.assistant.ui.screens.SettingsScreen
import com.zara.assistant.ui.viewmodel.MainViewModel
import com.zara.assistant.ui.viewmodel.SettingsViewModel

/**
 * Main navigation component for Zara app
 */
@Composable
fun ZaraNavigation(
    mainViewModel: MainViewModel,
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController(),
    startDestination: String = ZaraDestinations.MAIN_ROUTE
) {
    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier
    ) {
        composable(ZaraDestinations.ONBOARDING_ROUTE) {
            OnboardingScreen(
                onNavigateToPermissions = {
                    navController.navigate(ZaraDestinations.PERMISSIONS_ROUTE)
                },
                onNavigateToMain = {
                    mainViewModel.completeOnboarding()
                    navController.navigate(ZaraDestinations.MAIN_ROUTE) {
                        popUpTo(ZaraDestinations.ONBOARDING_ROUTE) { inclusive = true }
                    }
                }
            )
        }
        
        composable(ZaraDestinations.PERMISSIONS_ROUTE) {
            PermissionsScreen(
                onPermissionsGranted = {
                    navController.navigate(ZaraDestinations.API_KEY_SETUP_ROUTE) {
                        popUpTo(ZaraDestinations.PERMISSIONS_ROUTE) { inclusive = true }
                    }
                },
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        composable(ZaraDestinations.API_KEY_SETUP_ROUTE) {
            ApiKeySetupScreen(
                onSetupComplete = {
                    navController.navigate(ZaraDestinations.MAIN_ROUTE) {
                        popUpTo(ZaraDestinations.API_KEY_SETUP_ROUTE) { inclusive = true }
                    }
                }
            )
        }
        
        composable(ZaraDestinations.MAIN_ROUTE) {
            val viewModel: MainViewModel = hiltViewModel()
            MainScreen(
                viewModel = viewModel,
                onNavigateToSettings = {
                    navController.navigate(ZaraDestinations.SETTINGS_ROUTE)
                },
                onNavigateToCommands = {
                    navController.navigate(ZaraDestinations.COMMANDS_HELP_ROUTE)
                }
            )
        }
        
        composable(ZaraDestinations.SETTINGS_ROUTE) {
            val viewModel: SettingsViewModel = hiltViewModel()
            SettingsScreen(
                viewModel = viewModel,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToAbout = {
                    navController.navigate(ZaraDestinations.ABOUT_ROUTE)
                },
                onNavigateToApiKeySetup = {
                    navController.navigate(ZaraDestinations.API_KEY_SETUP_ROUTE)
                }
            )
        }
        
        composable(ZaraDestinations.COMMANDS_HELP_ROUTE) {
            CommandsHelpScreen(
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }
        
        composable(ZaraDestinations.ABOUT_ROUTE) {
            AboutScreen(
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }
    }
}

/**
 * Destinations used in the Zara app
 */
object ZaraDestinations {
    const val ONBOARDING_ROUTE = "onboarding"
    const val PERMISSIONS_ROUTE = "permissions"
    const val API_KEY_SETUP_ROUTE = "api_key_setup"
    const val MAIN_ROUTE = "main"
    const val SETTINGS_ROUTE = "settings"
    const val COMMANDS_HELP_ROUTE = "commands_help"
    const val ABOUT_ROUTE = "about"
}
