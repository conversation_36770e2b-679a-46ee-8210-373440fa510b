package com.zara.assistant.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages runtime permissions with graceful degradation
 */
@Singleton
class PermissionManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "PermissionManager"
        
        // Core permissions required for basic functionality
        val CORE_PERMISSIONS = arrayOf(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.POST_NOTIFICATIONS
        )
        
        // Optional permissions for enhanced functionality
        val OPTIONAL_PERMISSIONS = arrayOf(
            Manifest.permission.CALL_PHONE,
            Manifest.permission.SEND_SMS,
            Manifest.permission.READ_CONTACTS,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
        
        // Special permissions that require Settings navigation
        val SPECIAL_PERMISSIONS = arrayOf(
            Manifest.permission.SYSTEM_ALERT_WINDOW,
            Manifest.permission.WRITE_SETTINGS,
            Manifest.permission.BIND_ACCESSIBILITY_SERVICE,
            Manifest.permission.BIND_NOTIFICATION_LISTENER_SERVICE
        )
    }
    
    data class PermissionStatus(
        val permission: String,
        val isGranted: Boolean,
        val isRequired: Boolean,
        val requiresSpecialHandling: Boolean = false
    )
    
    /**
     * Check if core permissions are granted
     */
    fun areCorePermissionsGranted(): Boolean {
        return CORE_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * Get status of all permissions
     */
    fun getPermissionStatuses(): List<PermissionStatus> {
        val statuses = mutableListOf<PermissionStatus>()
        
        // Core permissions
        CORE_PERMISSIONS.forEach { permission ->
            statuses.add(
                PermissionStatus(
                    permission = permission,
                    isGranted = ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED,
                    isRequired = true
                )
            )
        }
        
        // Optional permissions
        OPTIONAL_PERMISSIONS.forEach { permission ->
            statuses.add(
                PermissionStatus(
                    permission = permission,
                    isGranted = ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED,
                    isRequired = false
                )
            )
        }
        
        // Special permissions
        SPECIAL_PERMISSIONS.forEach { permission ->
            statuses.add(
                PermissionStatus(
                    permission = permission,
                    isGranted = isSpecialPermissionGranted(permission),
                    isRequired = false,
                    requiresSpecialHandling = true
                )
            )
        }
        
        return statuses
    }
    
    /**
     * Check if a special permission is granted
     */
    private fun isSpecialPermissionGranted(permission: String): Boolean {
        return when (permission) {
            Manifest.permission.SYSTEM_ALERT_WINDOW -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    Settings.canDrawOverlays(context)
                } else {
                    true
                }
            }
            Manifest.permission.WRITE_SETTINGS -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    Settings.System.canWrite(context)
                } else {
                    true
                }
            }
            Manifest.permission.BIND_ACCESSIBILITY_SERVICE -> {
                isAccessibilityServiceEnabled()
            }
            Manifest.permission.BIND_NOTIFICATION_LISTENER_SERVICE -> {
                isNotificationListenerEnabled()
            }
            else -> false
        }
    }
    
    /**
     * Check if accessibility service is enabled
     */
    private fun isAccessibilityServiceEnabled(): Boolean {
        return try {
            val enabledServices = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )
            enabledServices?.contains(context.packageName) == true
        } catch (e: Exception) {
            Log.e(TAG, "Error checking accessibility service status", e)
            false
        }
    }
    
    /**
     * Check if notification listener is enabled
     */
    private fun isNotificationListenerEnabled(): Boolean {
        return try {
            val enabledListeners = Settings.Secure.getString(
                context.contentResolver,
                "enabled_notification_listeners"
            )
            enabledListeners?.contains(context.packageName) == true
        } catch (e: Exception) {
            Log.e(TAG, "Error checking notification listener status", e)
            false
        }
    }
    
    /**
     * Request core permissions
     */
    fun requestCorePermissions(activity: Activity, requestCode: Int) {
        val missingPermissions = CORE_PERMISSIONS.filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }.toTypedArray()
        
        if (missingPermissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(activity, missingPermissions, requestCode)
        }
    }
    
    /**
     * Request optional permissions
     */
    fun requestOptionalPermissions(activity: Activity, requestCode: Int) {
        val missingPermissions = OPTIONAL_PERMISSIONS.filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }.toTypedArray()
        
        if (missingPermissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(activity, missingPermissions, requestCode)
        }
    }
    
    /**
     * Open settings for special permission
     */
    fun openSpecialPermissionSettings(activity: Activity, permission: String) {
        val intent = when (permission) {
            Manifest.permission.SYSTEM_ALERT_WINDOW -> {
                Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                    data = Uri.parse("package:${context.packageName}")
                }
            }
            Manifest.permission.WRITE_SETTINGS -> {
                Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS).apply {
                    data = Uri.parse("package:${context.packageName}")
                }
            }
            Manifest.permission.BIND_ACCESSIBILITY_SERVICE -> {
                Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
            }
            Manifest.permission.BIND_NOTIFICATION_LISTENER_SERVICE -> {
                Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS)
            }
            else -> {
                Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.parse("package:${context.packageName}")
                }
            }
        }
        
        try {
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening settings for permission: $permission", e)
            // Fallback to app settings
            val fallbackIntent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:${context.packageName}")
            }
            activity.startActivity(fallbackIntent)
        }
    }
    
    /**
     * Get user-friendly permission name
     */
    fun getPermissionDisplayName(permission: String): String {
        return when (permission) {
            Manifest.permission.RECORD_AUDIO -> "Microphone"
            Manifest.permission.POST_NOTIFICATIONS -> "Notifications"
            Manifest.permission.CALL_PHONE -> "Phone Calls"
            Manifest.permission.SEND_SMS -> "SMS Messages"
            Manifest.permission.READ_CONTACTS -> "Contacts"
            Manifest.permission.ACCESS_FINE_LOCATION -> "Precise Location"
            Manifest.permission.ACCESS_COARSE_LOCATION -> "Approximate Location"
            Manifest.permission.READ_EXTERNAL_STORAGE -> "Storage (Read)"
            Manifest.permission.WRITE_EXTERNAL_STORAGE -> "Storage (Write)"
            Manifest.permission.SYSTEM_ALERT_WINDOW -> "Display over other apps"
            Manifest.permission.WRITE_SETTINGS -> "Modify system settings"
            Manifest.permission.BIND_ACCESSIBILITY_SERVICE -> "Accessibility Service"
            Manifest.permission.BIND_NOTIFICATION_LISTENER_SERVICE -> "Notification Access"
            else -> permission.substringAfterLast(".")
        }
    }
    
    /**
     * Get permission description
     */
    fun getPermissionDescription(permission: String): String {
        return when (permission) {
            Manifest.permission.RECORD_AUDIO -> "Required for voice commands and wake word detection"
            Manifest.permission.POST_NOTIFICATIONS -> "Required for system notifications and alerts"
            Manifest.permission.CALL_PHONE -> "Allows making phone calls via voice commands"
            Manifest.permission.SEND_SMS -> "Allows sending text messages via voice commands"
            Manifest.permission.READ_CONTACTS -> "Allows calling contacts by name"
            Manifest.permission.ACCESS_FINE_LOCATION -> "Enables location-based responses and services"
            Manifest.permission.ACCESS_COARSE_LOCATION -> "Enables basic location-based responses"
            Manifest.permission.READ_EXTERNAL_STORAGE -> "Allows reading files and media"
            Manifest.permission.WRITE_EXTERNAL_STORAGE -> "Allows saving files and media"
            Manifest.permission.SYSTEM_ALERT_WINDOW -> "Enables voice assistant overlay interface"
            Manifest.permission.WRITE_SETTINGS -> "Allows controlling device settings via voice"
            Manifest.permission.BIND_ACCESSIBILITY_SERVICE -> "Enables advanced device control features"
            Manifest.permission.BIND_NOTIFICATION_LISTENER_SERVICE -> "Allows reading and managing notifications"
            else -> "Required for enhanced functionality"
        }
    }
}
