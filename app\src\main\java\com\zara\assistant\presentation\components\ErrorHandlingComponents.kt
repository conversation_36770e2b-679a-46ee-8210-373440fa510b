package com.zara.assistant.presentation.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay

/**
 * Enhanced error display with smooth animations and recovery options
 */
@Composable
fun ErrorCard(
    title: String,
    message: String,
    modifier: Modifier = Modifier,
    errorType: ErrorType = ErrorType.WARNING,
    onRetry: (() -> Unit)? = null,
    onDismiss: (() -> Unit)? = null,
    autoHide: Boolean = false,
    autoHideDelay: Long = 5000L
) {
    var isVisible by remember { mutableStateOf(true) }
    
    // Auto-hide functionality
    LaunchedEffect(autoHide) {
        if (autoHide) {
            delay(autoHideDelay)
            isVisible = false
            delay(300) // Animation duration
            onDismiss?.invoke()
        }
    }
    
    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            initialOffsetY = { -it },
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessMedium
            )
        ) + fadeIn(),
        exit = slideOutVertically(
            targetOffsetY = { -it },
            animationSpec = tween(300)
        ) + fadeOut()
    ) {
        NeumorphismCard(
            modifier = modifier.fillMaxWidth(),
            backgroundColor = errorType.backgroundColor,
            shadowColor = errorType.shadowColor,
            highlightColor = errorType.highlightColor,
            cornerRadius = 12.dp,
            elevation = 6.dp
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = errorType.icon,
                    contentDescription = null,
                    tint = errorType.iconColor,
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.SemiBold,
                        color = errorType.textColor
                    )
                    
                    if (message.isNotBlank()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = message,
                            style = MaterialTheme.typography.bodySmall,
                            color = errorType.textColor.copy(alpha = 0.8f)
                        )
                    }
                }
                
                // Action buttons
                Row {
                    onRetry?.let {
                        IconButton(
                            onClick = it,
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Refresh,
                                contentDescription = "Retry",
                                tint = errorType.iconColor,
                                modifier = Modifier.size(18.dp)
                            )
                        }
                    }
                    
                    onDismiss?.let {
                        IconButton(
                            onClick = {
                                isVisible = false
                                it()
                            },
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "Dismiss",
                                tint = errorType.iconColor,
                                modifier = Modifier.size(18.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * Service status indicator with health monitoring
 */
@Composable
fun ServiceStatusIndicator(
    serviceName: String,
    isRunning: Boolean,
    errorMessage: String? = null,
    retryCount: Int = 0,
    onRestart: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    val statusColor = when {
        isRunning -> Color(0xFF4CAF50) // Green
        errorMessage != null -> Color(0xFFF44336) // Red
        else -> Color(0xFFFF9800) // Orange
    }
    
    val statusIcon = when {
        isRunning -> Icons.Default.CheckCircle
        errorMessage != null -> Icons.Default.Error
        else -> Icons.Default.Warning
    }
    
    NeumorphismCard(
        modifier = modifier.fillMaxWidth(),
        cornerRadius = 8.dp,
        elevation = 4.dp,
        onClick = onRestart
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = statusIcon,
                contentDescription = null,
                tint = statusColor,
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = serviceName,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                val statusText = when {
                    isRunning -> "Running"
                    errorMessage != null -> "Error: $errorMessage"
                    else -> "Stopped"
                }
                
                Text(
                    text = statusText,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                
                if (retryCount > 0) {
                    Text(
                        text = "Retry attempts: $retryCount",
                        style = MaterialTheme.typography.labelSmall,
                        color = Color(0xFFFF9800)
                    )
                }
            }
            
            if (!isRunning && onRestart != null) {
                IconButton(onClick = onRestart) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "Restart",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}

/**
 * Connection status banner
 */
@Composable
fun ConnectionStatusBanner(
    isConnected: Boolean,
    connectionType: String,
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = !isConnected,
        enter = slideInVertically(initialOffsetY = { -it }) + fadeIn(),
        exit = slideOutVertically(targetOffsetY = { -it }) + fadeOut()
    ) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .background(
                    color = Color(0xFFFF5722),
                    shape = RoundedCornerShape(bottomStart = 8.dp, bottomEnd = 8.dp)
                )
                .padding(12.dp),
            contentAlignment = Alignment.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.CloudOff,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "No $connectionType connection",
                    color = Color.White,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * Loading state with error fallback
 */
@Composable
fun LoadingWithError(
    isLoading: Boolean,
    error: String? = null,
    onRetry: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Box(modifier = modifier) {
        when {
            isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    NeumorphismLoadingIndicator()
                }
            }
            error != null -> {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        tint = Color(0xFFF44336),
                        modifier = Modifier.size(48.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = "Something went wrong",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = error,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        textAlign = TextAlign.Center
                    )
                    
                    if (onRetry != null) {
                        Spacer(modifier = Modifier.height(24.dp))
                        
                        EnhancedNeumorphismButton(
                            text = "Try Again",
                            onClick = onRetry,
                            icon = {
                                Icon(
                                    imageVector = Icons.Default.Refresh,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                            }
                        )
                    }
                }
            }
            else -> content()
        }
    }
}

// Error type definitions
enum class ErrorType(
    val backgroundColor: Color,
    val shadowColor: Color,
    val highlightColor: Color,
    val iconColor: Color,
    val textColor: Color,
    val icon: ImageVector
) {
    ERROR(
        backgroundColor = Color(0xFFFFEBEE),
        shadowColor = Color(0xFFE57373),
        highlightColor = Color(0xFFFFCDD2),
        iconColor = Color(0xFFF44336),
        textColor = Color(0xFFD32F2F),
        icon = Icons.Default.Error
    ),
    WARNING(
        backgroundColor = Color(0xFFFFF3E0),
        shadowColor = Color(0xFFFFB74D),
        highlightColor = Color(0xFFFFE0B2),
        iconColor = Color(0xFFFF9800),
        textColor = Color(0xFFF57C00),
        icon = Icons.Default.Warning
    ),
    INFO(
        backgroundColor = Color(0xFFE3F2FD),
        shadowColor = Color(0xFF64B5F6),
        highlightColor = Color(0xFFBBDEFB),
        iconColor = Color(0xFF2196F3),
        textColor = Color(0xFF1976D2),
        icon = Icons.Default.Info
    ),
    SUCCESS(
        backgroundColor = Color(0xFFE8F5E8),
        shadowColor = Color(0xFF81C784),
        highlightColor = Color(0xFFC8E6C9),
        iconColor = Color(0xFF4CAF50),
        textColor = Color(0xFF388E3C),
        icon = Icons.Default.CheckCircle
    )
}
