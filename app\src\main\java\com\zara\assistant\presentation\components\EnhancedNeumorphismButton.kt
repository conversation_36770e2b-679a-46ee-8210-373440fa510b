package com.zara.assistant.presentation.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.zara.assistant.presentation.theme.NeuroLightHighlight
import com.zara.assistant.presentation.theme.NeuroLightShadow
import com.zara.assistant.presentation.theme.NeuroSurfaceLight

/**
 * Enhanced Neumorphism Button with smooth animations and ripple effects
 */
@Composable
fun EnhancedNeumorphismButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    cornerRadius: Dp = 16.dp,
    elevation: Dp = 6.dp,
    backgroundColor: Color = NeuroSurfaceLight,
    shadowColor: Color = NeuroLightShadow,
    highlightColor: Color = NeuroLightHighlight,
    contentPadding: Dp = 16.dp,
    textColor: Color = MaterialTheme.colorScheme.onSurface,
    icon: (@Composable () -> Unit)? = null,
    isSecondary: Boolean = false
) {
    val interactionSource = remember { MutableInteractionSource() }
    var isPressed by remember { mutableStateOf(false) }
    var isHovered by remember { mutableStateOf(false) }
    
    // Ripple animation
    val rippleAlpha by animateFloatAsState(
        targetValue = if (isPressed) 0.15f else 0f,
        animationSpec = tween(durationMillis = 150),
        label = "rippleAlpha"
    )
    
    // Press scale animation with bounce
    val pressScale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else if (isHovered) 1.02f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "pressScale"
    )
    
    // Elevation animation
    val animatedElevation by animateDpAsState(
        targetValue = when {
            isPressed -> elevation * 0.2f
            isHovered -> elevation * 1.2f
            else -> elevation
        },
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "elevation"
    )
    
    // Glow effect for secondary buttons
    val glowAlpha by animateFloatAsState(
        targetValue = if (isSecondary && isHovered) 0.3f else 0f,
        animationSpec = tween(durationMillis = 300),
        label = "glowAlpha"
    )

    // Pre-calculate colors for use in drawBehind
    val primaryColor = MaterialTheme.colorScheme.primary
    val secondaryColor = MaterialTheme.colorScheme.secondary
    
    Box(
        modifier = modifier
            .scale(pressScale)
            .clip(RoundedCornerShape(cornerRadius))
            .clickable(
                interactionSource = interactionSource,
                indication = null,
                enabled = enabled
            ) { 
                if (enabled) onClick() 
            }
            .drawBehind {
                val sizeValue = this.size
                drawIntoCanvas { canvas ->
                    val paint = Paint()
                    val frameworkPaint = paint.asFrameworkPaint()
                    val elevationPx = animatedElevation.toPx()
                    
                    // Glow effect for secondary buttons
                    if (glowAlpha > 0f) {
                        frameworkPaint.color = primaryColor.copy(alpha = glowAlpha).toArgb()
                        frameworkPaint.setShadowLayer(
                            elevationPx * 2f,
                            0f,
                            0f,
                            primaryColor.copy(alpha = glowAlpha * 0.5f).toArgb()
                        )
                        canvas.drawRoundRect(
                            left = -elevationPx,
                            top = -elevationPx,
                            right = sizeValue.width + elevationPx,
                            bottom = sizeValue.height + elevationPx,
                            radiusX = cornerRadius.toPx(),
                            radiusY = cornerRadius.toPx(),
                            paint = paint
                        )
                    }
                    
                    // Multi-layer shadow for depth
                    val shadowLayers = listOf(
                        Triple(elevationPx * 0.2f, elevationPx * 0.2f, elevationPx * 0.5f),
                        Triple(elevationPx * 0.5f, elevationPx * 0.5f, elevationPx * 1.0f),
                        Triple(elevationPx * 0.8f, elevationPx * 0.8f, elevationPx * 1.5f)
                    )
                    
                    shadowLayers.forEach { (offsetX, offsetY, blur) ->
                        frameworkPaint.color = shadowColor.copy(
                            alpha = if (enabled) 0.2f else 0.1f
                        ).toArgb()
                        frameworkPaint.setShadowLayer(blur, offsetX, offsetY, shadowColor.toArgb())
                        canvas.drawRoundRect(
                            left = offsetX,
                            top = offsetY,
                            right = sizeValue.width,
                            bottom = sizeValue.height,
                            radiusX = cornerRadius.toPx(),
                            radiusY = cornerRadius.toPx(),
                            paint = paint
                        )
                    }
                    
                    // Multi-layer highlight
                    val highlightLayers = listOf(
                        Triple(-elevationPx * 0.2f, -elevationPx * 0.2f, elevationPx * 0.5f),
                        Triple(-elevationPx * 0.5f, -elevationPx * 0.5f, elevationPx * 1.0f)
                    )
                    
                    highlightLayers.forEach { (offsetX, offsetY, blur) ->
                        frameworkPaint.color = highlightColor.copy(
                            alpha = if (enabled) 0.9f else 0.4f
                        ).toArgb()
                        frameworkPaint.setShadowLayer(blur, offsetX, offsetY, highlightColor.toArgb())
                        canvas.drawRoundRect(
                            left = 0f,
                            top = 0f,
                            right = sizeValue.width + offsetX,
                            bottom = sizeValue.height + offsetY,
                            radiusX = cornerRadius.toPx(),
                            radiusY = cornerRadius.toPx(),
                            paint = paint
                        )
                    }
                    
                    // Ripple effect
                    if (rippleAlpha > 0f) {
                        val rippleColor = if (isSecondary) {
                            secondaryColor
                        } else {
                            primaryColor
                        }
                        frameworkPaint.color = rippleColor.copy(alpha = rippleAlpha).toArgb()
                        frameworkPaint.clearShadowLayer()
                        canvas.drawRoundRect(
                            left = 0f,
                            top = 0f,
                            right = sizeValue.width,
                            bottom = sizeValue.height,
                            radiusX = cornerRadius.toPx(),
                            radiusY = cornerRadius.toPx(),
                            paint = paint
                        )
                    }
                }
            }
            .background(
                color = backgroundColor.copy(alpha = if (enabled) 1f else 0.6f),
                shape = RoundedCornerShape(cornerRadius)
            )
            .padding(contentPadding),
        contentAlignment = Alignment.Center
    ) {
        // Handle interaction states
        LaunchedEffect(interactionSource) {
            interactionSource.interactions.collect { interaction ->
                when (interaction) {
                    is androidx.compose.foundation.interaction.PressInteraction.Press -> {
                        isPressed = true
                    }
                    is androidx.compose.foundation.interaction.PressInteraction.Release -> {
                        isPressed = false
                    }
                    is androidx.compose.foundation.interaction.PressInteraction.Cancel -> {
                        isPressed = false
                    }
                    is androidx.compose.foundation.interaction.HoverInteraction.Enter -> {
                        isHovered = true
                    }
                    is androidx.compose.foundation.interaction.HoverInteraction.Exit -> {
                        isHovered = false
                    }
                }
            }
        }
        
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier.fillMaxWidth()
        ) {
            icon?.let {
                it()
                Spacer(modifier = Modifier.width(8.dp))
            }
            
            Text(
                text = text,
                style = MaterialTheme.typography.labelLarge,
                color = textColor.copy(alpha = if (enabled) 1f else 0.6f),
                fontWeight = if (isSecondary) FontWeight.Medium else FontWeight.SemiBold
            )
        }
    }
}

/**
 * Floating Action Button with Neumorphism style
 */
@Composable
fun NeumorphismFAB(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    size: Dp = 56.dp,
    elevation: Dp = 8.dp,
    backgroundColor: Color = MaterialTheme.colorScheme.primary,
    shadowColor: Color = NeuroLightShadow,
    highlightColor: Color = NeuroLightHighlight,
    content: @Composable () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    var isPressed by remember { mutableStateOf(false) }
    
    val pressScale by animateFloatAsState(
        targetValue = if (isPressed) 0.9f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "fabPressScale"
    )
    
    val animatedElevation by animateDpAsState(
        targetValue = if (isPressed) elevation * 0.3f else elevation,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "fabElevation"
    )
    
    Box(
        modifier = modifier
            .size(size)
            .scale(pressScale)
            .clip(RoundedCornerShape(size / 2))
            .clickable(
                interactionSource = interactionSource,
                indication = null,
                enabled = enabled
            ) { onClick() }
            .drawBehind {
                val sizeValue = this.size
                drawIntoCanvas { canvas ->
                    val paint = Paint()
                    val frameworkPaint = paint.asFrameworkPaint()
                    val elevationPx = animatedElevation.toPx()
                    val radius = kotlin.math.min(sizeValue.width, sizeValue.height) / 2f
                    
                    // Shadow
                    frameworkPaint.color = shadowColor.copy(alpha = 0.3f).toArgb()
                    frameworkPaint.setShadowLayer(
                        elevationPx * 1.5f,
                        elevationPx,
                        elevationPx,
                        shadowColor.toArgb()
                    )
                    canvas.drawCircle(
                        center = androidx.compose.ui.geometry.Offset(
                            sizeValue.width / 2 + elevationPx * 0.5f,
                            sizeValue.height / 2 + elevationPx * 0.5f
                        ),
                        radius = radius,
                        paint = paint
                    )
                    
                    // Highlight
                    frameworkPaint.color = highlightColor.copy(alpha = 0.8f).toArgb()
                    frameworkPaint.setShadowLayer(
                        elevationPx,
                        -elevationPx * 0.5f,
                        -elevationPx * 0.5f,
                        highlightColor.toArgb()
                    )
                    canvas.drawCircle(
                        center = androidx.compose.ui.geometry.Offset(
                            sizeValue.width / 2 - elevationPx * 0.5f,
                            sizeValue.height / 2 - elevationPx * 0.5f
                        ),
                        radius = radius,
                        paint = paint
                    )
                }
            }
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(size / 2)
            ),
        contentAlignment = Alignment.Center
    ) {
        LaunchedEffect(interactionSource) {
            interactionSource.interactions.collect { interaction ->
                when (interaction) {
                    is androidx.compose.foundation.interaction.PressInteraction.Press -> {
                        isPressed = true
                    }
                    is androidx.compose.foundation.interaction.PressInteraction.Release -> {
                        isPressed = false
                    }
                    is androidx.compose.foundation.interaction.PressInteraction.Cancel -> {
                        isPressed = false
                    }
                }
            }
        }
        
        content()
    }
}
