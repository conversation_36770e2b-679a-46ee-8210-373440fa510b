package com.zara.assistant.ui.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.zara.assistant.ui.viewmodel.MainViewModel
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Help
import androidx.compose.material.icons.filled.Settings
import androidx.compose.runtime.collectAsState
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.zara.assistant.R
import com.zara.assistant.core.Constants
import com.zara.assistant.presentation.components.NeumorphismCard
import com.zara.assistant.presentation.components.VoiceButton
import com.zara.assistant.presentation.components.VoiceVisualization
import com.zara.assistant.presentation.components.EnhancedVoiceVisualization
import com.zara.assistant.presentation.components.VisualizationStyle
import com.zara.assistant.presentation.components.EnhancedNeumorphismButton
import com.zara.assistant.presentation.components.ErrorCard
import com.zara.assistant.presentation.components.ErrorType
import com.zara.assistant.ui.viewmodel.MainUiState

/**
 * Main screen of the Zara AI Voice Assistant
 */
@Composable
fun MainScreen(
    viewModel: MainViewModel,
    onNavigateToSettings: () -> Unit,
    onNavigateToCommands: () -> Unit,
    modifier: Modifier = Modifier
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Show error messages
    LaunchedEffect(uiState.errorMessage) {
        uiState.errorMessage?.let { message ->
            snackbarHostState.showSnackbar(
                message = message,
                duration = androidx.compose.material3.SnackbarDuration.Short
            )
        }
    }

    Scaffold(
        modifier = modifier.fillMaxSize(),
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Main content
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .statusBarsPadding()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                // Top section with settings
                TopSection(
                    onSettingsClick = onNavigateToSettings,
                    onCommandsClick = onNavigateToCommands
                )

                // Center section with voice interaction
                CenterSection(
                    uiState = uiState,
                    onVoiceButtonClick = { viewModel.onVoiceButtonClicked() }
                )

                // Bottom section with controls
                BottomSection(
                    uiState = uiState,
                    onWakeWordToggle = { enabled -> viewModel.onWakeWordToggle(enabled) }
                )
            }

            // Permission dialog
            if (uiState.showPermissionDialog) {
                PermissionDialog(
                    missingPermissions = uiState.missingPermissions,
                    onRequestPermissions = { /* Handle permission request */ },
                    onDismiss = { viewModel.dismissPermissionDialog() }
                )
            }
        }
    }
}

@Composable
private fun TopSection(
    onSettingsClick: () -> Unit,
    onCommandsClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Zara logo/title
        Text(
            text = "Zara",
            style = MaterialTheme.typography.headlineLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )

        // Action buttons
        Row {
            IconButton(onClick = onCommandsClick) {
                Icon(
                    imageVector = Icons.Default.Help,
                    contentDescription = "Voice Commands",
                    tint = MaterialTheme.colorScheme.onSurface
                )
            }

            IconButton(onClick = onSettingsClick) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = stringResource(R.string.settings),
                    tint = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}

@Composable
private fun CenterSection(
    uiState: MainUiState,
    onVoiceButtonClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Voice visualization
        AnimatedVisibility(
            visible = uiState.voiceState.isListening ||
                     uiState.voiceState.isProcessing ||
                     uiState.voiceState.isSpeaking,
            enter = fadeIn(),
            exit = fadeOut()
        ) {
            EnhancedVoiceVisualization(
                voiceState = mapToConstantsVoiceState(uiState.voiceState.currentState),
                style = VisualizationStyle.WAVE_BARS,
                modifier = Modifier.padding(bottom = 32.dp)
            )
        }

        // Main voice button
        VoiceButton(
            voiceState = mapToConstantsVoiceState(uiState.voiceState.currentState),
            onClick = onVoiceButtonClick,
            modifier = Modifier.size(120.dp)
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Voice state text
        Text(
            text = uiState.voiceStateText,
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurface
        )

        // Current command display
        uiState.voiceState.currentCommand?.let { command ->
            Spacer(modifier = Modifier.height(16.dp))
            NeumorphismCard(
                modifier = Modifier.fillMaxWidth(),
                contentPadding = 16.dp
            ) {
                Text(
                    text = "\"$command\"",
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
private fun BottomSection(
    uiState: MainUiState,
    onWakeWordToggle: (Boolean) -> Unit
) {
    NeumorphismCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column {
            // Wake word toggle
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = stringResource(R.string.wake_word_enabled),
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = if (uiState.isWakeWordActive) {
                            stringResource(R.string.wake_word_active)
                        } else {
                            stringResource(R.string.wake_word_inactive)
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Switch(
                    checked = uiState.isWakeWordEnabled,
                    onCheckedChange = onWakeWordToggle
                )
            }

            // Service status
            if (uiState.isWakeWordServiceRunning) {
                Spacer(modifier = Modifier.height(12.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                color = if (uiState.isWakeWordActive) {
                                    MaterialTheme.colorScheme.primary
                                } else {
                                    MaterialTheme.colorScheme.outline
                                },
                                shape = androidx.compose.foundation.shape.CircleShape
                            )
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = if (uiState.isWakeWordActive) {
                            "Listening for \"Hey Zara\""
                        } else {
                            "Wake word detection paused"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
private fun PermissionDialog(
    missingPermissions: List<String>,
    onRequestPermissions: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(text = "Permissions Required")
        },
        text = {
            Column {
                Text(text = "Zara needs the following permissions to work properly:")
                Spacer(modifier = Modifier.height(8.dp))
                missingPermissions.forEach { permission ->
                    Text(
                        text = "• ${getPermissionDisplayName(permission)}",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        },
        confirmButton = {
            EnhancedNeumorphismButton(
                text = "Grant Permissions",
                onClick = onRequestPermissions,
                modifier = Modifier.padding(8.dp)
            )
        },
        dismissButton = {
            EnhancedNeumorphismButton(
                text = "Cancel",
                onClick = onDismiss,
                isSecondary = true,
                modifier = Modifier.padding(8.dp)
            )
        }
    )
}

private fun getPermissionDisplayName(permission: String): String {
    return when (permission) {
        android.Manifest.permission.RECORD_AUDIO -> "Microphone access"
        android.Manifest.permission.POST_NOTIFICATIONS -> "Notification access"
        android.Manifest.permission.SYSTEM_ALERT_WINDOW -> "Display over other apps"
        android.Manifest.permission.CALL_PHONE -> "Phone access"
        android.Manifest.permission.SEND_SMS -> "SMS access"
        android.Manifest.permission.READ_CONTACTS -> "Contacts access"
        android.Manifest.permission.ACCESS_FINE_LOCATION -> "Location access"
        android.Manifest.permission.ACCESS_COARSE_LOCATION -> "Location access"
        else -> permission
    }
}

private fun mapToConstantsVoiceState(state: com.zara.assistant.domain.model.VoiceState.State): Constants.VoiceState {
    return when (state) {
        com.zara.assistant.domain.model.VoiceState.State.IDLE -> Constants.VoiceState.IDLE
        com.zara.assistant.domain.model.VoiceState.State.LISTENING_WAKE_WORD -> Constants.VoiceState.LISTENING_WAKE_WORD
        com.zara.assistant.domain.model.VoiceState.State.LISTENING_COMMAND -> Constants.VoiceState.LISTENING_COMMAND
        com.zara.assistant.domain.model.VoiceState.State.PROCESSING_COMMAND -> Constants.VoiceState.PROCESSING
        com.zara.assistant.domain.model.VoiceState.State.GENERATING_RESPONSE -> Constants.VoiceState.PROCESSING
        com.zara.assistant.domain.model.VoiceState.State.SPEAKING_RESPONSE -> Constants.VoiceState.SPEAKING
        com.zara.assistant.domain.model.VoiceState.State.EXECUTING_ACTION -> Constants.VoiceState.PROCESSING
        com.zara.assistant.domain.model.VoiceState.State.ERROR -> Constants.VoiceState.ERROR
        com.zara.assistant.domain.model.VoiceState.State.DISABLED -> Constants.VoiceState.IDLE
    }
}
