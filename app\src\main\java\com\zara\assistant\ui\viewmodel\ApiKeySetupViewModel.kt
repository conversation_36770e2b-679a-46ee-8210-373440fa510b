package com.zara.assistant.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zara.assistant.utils.ApiKeyManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for API key setup screen
 */
@HiltViewModel
class ApiKeySetupViewModel @Inject constructor(
    private val apiKeyManager: ApiKeyManager
) : ViewModel() {

    data class UiState(
        val cohereApiKey: String = "",
        val perplexityApiKey: String = "",
        val porcupineAccessKey: String = "",
        val azureSpeechKey: String = "",
        val azureSpeechRegion: String = "eastus",
        val cohereApiKeyError: String? = null,
        val perplexityApiKeyError: String? = null,
        val porcupineAccessKeyError: String? = null,
        val azureSpeechKeyError: String? = null,
        val azureSpeechRegionError: String? = null,
        val isLoading: Boolean = false,
        val errorMessage: String? = null
    )

    private val _uiState = MutableStateFlow(UiState())
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()

    init {
        loadExistingKeys()
    }

    private fun loadExistingKeys() {
        viewModelScope.launch {
            try {
                val currentState = _uiState.value
                _uiState.value = currentState.copy(
                    cohereApiKey = apiKeyManager.getCohereApiKey(),
                    perplexityApiKey = apiKeyManager.getPerplexityApiKey(),
                    porcupineAccessKey = apiKeyManager.getPorcupineAccessKey(),
                    azureSpeechKey = apiKeyManager.getAzureSpeechKey(),
                    azureSpeechRegion = apiKeyManager.getAzureSpeechRegion().ifEmpty { "eastus" }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to load existing keys: ${e.message}"
                )
            }
        }
    }

    fun updateCohereApiKey(key: String) {
        _uiState.value = _uiState.value.copy(
            cohereApiKey = key,
            cohereApiKeyError = null,
            errorMessage = null
        )
    }

    fun updatePerplexityApiKey(key: String) {
        _uiState.value = _uiState.value.copy(
            perplexityApiKey = key,
            perplexityApiKeyError = null,
            errorMessage = null
        )
    }

    fun updatePorcupineAccessKey(key: String) {
        _uiState.value = _uiState.value.copy(
            porcupineAccessKey = key,
            porcupineAccessKeyError = null,
            errorMessage = null
        )
    }

    fun updateAzureSpeechKey(key: String) {
        _uiState.value = _uiState.value.copy(
            azureSpeechKey = key,
            azureSpeechKeyError = null,
            errorMessage = null
        )
    }

    fun updateAzureSpeechRegion(region: String) {
        _uiState.value = _uiState.value.copy(
            azureSpeechRegion = region,
            azureSpeechRegionError = null,
            errorMessage = null
        )
    }

    fun saveApiKeys(onSuccess: () -> Unit) {
        viewModelScope.launch {
            val currentState = _uiState.value
            
            // Clear previous errors
            _uiState.value = currentState.copy(
                isLoading = true,
                cohereApiKeyError = null,
                perplexityApiKeyError = null,
                porcupineAccessKeyError = null,
                azureSpeechKeyError = null,
                azureSpeechRegionError = null,
                errorMessage = null
            )

            try {
                // Validate inputs
                val errors = validateInputs(currentState)
                if (errors.isNotEmpty()) {
                    _uiState.value = currentState.copy(
                        isLoading = false,
                        cohereApiKeyError = errors["cohere"],
                        perplexityApiKeyError = errors["perplexity"],
                        porcupineAccessKeyError = errors["porcupine"],
                        azureSpeechKeyError = errors["azure_key"],
                        azureSpeechRegionError = errors["azure_region"]
                    )
                    return@launch
                }

                // Save keys securely
                var allSaved = true
                val failedKeys = mutableListOf<String>()

                if (currentState.cohereApiKey.isNotEmpty()) {
                    if (!apiKeyManager.storeCohereApiKey(currentState.cohereApiKey)) {
                        allSaved = false
                        failedKeys.add("Cohere")
                    }
                }

                if (currentState.perplexityApiKey.isNotEmpty()) {
                    if (!apiKeyManager.storePerplexityApiKey(currentState.perplexityApiKey)) {
                        allSaved = false
                        failedKeys.add("Perplexity")
                    }
                }

                if (currentState.porcupineAccessKey.isNotEmpty()) {
                    if (!apiKeyManager.storePorcupineAccessKey(currentState.porcupineAccessKey)) {
                        allSaved = false
                        failedKeys.add("Porcupine")
                    }
                }

                if (currentState.azureSpeechKey.isNotEmpty()) {
                    if (!apiKeyManager.storeAzureSpeechKey(currentState.azureSpeechKey)) {
                        allSaved = false
                        failedKeys.add("Azure Speech")
                    }
                }

                if (currentState.azureSpeechRegion.isNotEmpty()) {
                    if (!apiKeyManager.storeAzureSpeechRegion(currentState.azureSpeechRegion)) {
                        allSaved = false
                        failedKeys.add("Azure Region")
                    }
                }

                _uiState.value = currentState.copy(isLoading = false)

                if (allSaved) {
                    onSuccess()
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to save: ${failedKeys.joinToString(", ")}"
                    )
                }

            } catch (e: Exception) {
                _uiState.value = currentState.copy(
                    isLoading = false,
                    errorMessage = "Error saving API keys: ${e.message}"
                )
            }
        }
    }

    private fun validateInputs(state: UiState): Map<String, String> {
        val errors = mutableMapOf<String, String>()

        // Basic validation - at least one key should be provided
        val hasAnyKey = state.cohereApiKey.isNotEmpty() ||
                state.perplexityApiKey.isNotEmpty() ||
                state.porcupineAccessKey.isNotEmpty() ||
                state.azureSpeechKey.isNotEmpty()

        if (!hasAnyKey) {
            errors["cohere"] = "At least one API key is required"
            return errors
        }

        // Validate individual keys if provided
        if (state.cohereApiKey.isNotEmpty() && state.cohereApiKey.length < 10) {
            errors["cohere"] = "Invalid Cohere API key format"
        }

        if (state.perplexityApiKey.isNotEmpty() && !state.perplexityApiKey.startsWith("pplx-")) {
            errors["perplexity"] = "Perplexity API key should start with 'pplx-'"
        }

        if (state.porcupineAccessKey.isNotEmpty() && state.porcupineAccessKey.length < 20) {
            errors["porcupine"] = "Invalid Porcupine access key format"
        }

        if (state.azureSpeechKey.isNotEmpty() && state.azureSpeechKey.length < 20) {
            errors["azure_key"] = "Invalid Azure Speech API key format"
        }

        if (state.azureSpeechRegion.isEmpty()) {
            errors["azure_region"] = "Azure region is required"
        }

        return errors
    }

    fun clearErrorMessage() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
}
