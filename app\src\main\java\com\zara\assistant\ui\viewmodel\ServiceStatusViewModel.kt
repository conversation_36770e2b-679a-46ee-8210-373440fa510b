package com.zara.assistant.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zara.assistant.services.ServiceManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for service status screen
 */
@HiltViewModel
class ServiceStatusViewModel @Inject constructor(
    private val serviceManager: ServiceManager
) : ViewModel() {

    data class UiState(
        val serviceStatuses: List<ServiceManager.ServiceStatus> = emptyList(),
        val allServicesRunning: Boolean = false,
        val hasErrors: Boolean = false,
        val isLoading: Boolean = false,
        val errorMessage: String? = null
    )

    private val _uiState = MutableStateFlow(UiState())
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()

    init {
        refreshServiceStatus()
    }

    fun refreshServiceStatus() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
                
                val statuses = serviceManager.getServiceStatuses()
                val allRunning = statuses.all { it.isRunning }
                val hasErrors = statuses.any { !it.isRunning && it.isRequired }
                
                _uiState.value = _uiState.value.copy(
                    serviceStatuses = statuses,
                    allServicesRunning = allRunning,
                    hasErrors = hasErrors,
                    isLoading = false
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Failed to refresh service status: ${e.message}"
                )
            }
        }
    }

    fun restartService(serviceName: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
                
                serviceManager.restartService(serviceName)
                
                // Refresh status after restart
                refreshServiceStatus()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Failed to restart $serviceName: ${e.message}"
                )
            }
        }
    }

    fun restartAllServices() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
                
                serviceManager.stopServices()
                serviceManager.startServices()
                
                // Refresh status after restart
                refreshServiceStatus()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Failed to restart services: ${e.message}"
                )
            }
        }
    }

    fun clearErrorMessage() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
}
