package com.zara.assistant.presentation.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlin.math.cos
import kotlin.math.sin
import com.zara.assistant.presentation.theme.NeuroLightHighlight
import com.zara.assistant.presentation.theme.NeuroLightShadow
import com.zara.assistant.presentation.theme.NeuroSurfaceLight

/**
 * Enhanced Neumorphism style card component with animations
 */
@Composable
fun NeumorphismCard(
    modifier: Modifier = Modifier,
    cornerRadius: Dp = 20.dp,
    elevation: Dp = 8.dp,
    backgroundColor: Color = NeuroSurfaceLight,
    shadowColor: Color = NeuroLightShadow,
    highlightColor: Color = NeuroLightHighlight,
    contentPadding: Dp = 20.dp,
    onClick: (() -> Unit)? = null,
    enabled: Boolean = true,
    animatePress: Boolean = true,
    content: @Composable () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    var isPressed by remember { mutableStateOf(false) }

    // Animation for press effect
    val pressScale by animateFloatAsState(
        targetValue = if (isPressed && animatePress) 0.98f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "pressScale"
    )

    // Animation for elevation
    val animatedElevation by animateDpAsState(
        targetValue = if (isPressed && animatePress) elevation * 0.5f else elevation,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "elevation"
    )
    Box(
        modifier = modifier
            .scale(pressScale)
            .fillMaxWidth()
            .clip(RoundedCornerShape(cornerRadius))
            .then(
                if (onClick != null && enabled) {
                    Modifier.clickable(
                        interactionSource = interactionSource,
                        indication = null
                    ) {
                        onClick()
                    }
                } else Modifier
            )
            .drawBehind {
                drawIntoCanvas { canvas ->
                    val paint = Paint()
                    val frameworkPaint = paint.asFrameworkPaint()
                    val elevationPx = animatedElevation.toPx()
                    val blurRadius = elevationPx * 1.5f

                    // Enhanced shadow with multiple layers for depth
                    val shadowLayers = listOf(
                        Triple(elevationPx * 0.3f, elevationPx * 0.3f, blurRadius * 0.3f),
                        Triple(elevationPx * 0.6f, elevationPx * 0.6f, blurRadius * 0.6f),
                        Triple(elevationPx, elevationPx, blurRadius)
                    )

                    shadowLayers.forEach { (offsetX, offsetY, blur) ->
                        frameworkPaint.color = shadowColor.copy(alpha = 0.15f).toArgb()
                        frameworkPaint.setShadowLayer(blur, offsetX, offsetY, shadowColor.toArgb())
                        canvas.drawRoundRect(
                            left = offsetX,
                            top = offsetY,
                            right = size.width,
                            bottom = size.height,
                            radiusX = cornerRadius.toPx(),
                            radiusY = cornerRadius.toPx(),
                            paint = paint
                        )
                    }

                    // Enhanced highlight with gradient effect
                    val highlightLayers = listOf(
                        Triple(-elevationPx * 0.3f, -elevationPx * 0.3f, blurRadius * 0.3f),
                        Triple(-elevationPx * 0.6f, -elevationPx * 0.6f, blurRadius * 0.6f),
                        Triple(-elevationPx, -elevationPx, blurRadius)
                    )

                    highlightLayers.forEach { (offsetX, offsetY, blur) ->
                        frameworkPaint.color = highlightColor.copy(alpha = 0.8f).toArgb()
                        frameworkPaint.setShadowLayer(blur, offsetX, offsetY, highlightColor.toArgb())
                        canvas.drawRoundRect(
                            left = 0f,
                            top = 0f,
                            right = size.width + offsetX,
                            bottom = size.height + offsetY,
                            radiusX = cornerRadius.toPx(),
                            radiusY = cornerRadius.toPx(),
                            paint = paint
                        )
                    }
                }
            }
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(cornerRadius)
            )
            .padding(contentPadding)
    ) {
        // Handle press state
        LaunchedEffect(interactionSource) {
            interactionSource.interactions.collect { interaction ->
                when (interaction) {
                    is androidx.compose.foundation.interaction.PressInteraction.Press -> {
                        isPressed = true
                    }
                    is androidx.compose.foundation.interaction.PressInteraction.Release -> {
                        isPressed = false
                    }
                    is androidx.compose.foundation.interaction.PressInteraction.Cancel -> {
                        isPressed = false
                    }
                }
            }
        }

        content()
    }
}

/**
 * Inset neumorphism card (pressed/concave effect)
 */
@Composable
fun NeumorphismInsetCard(
    modifier: Modifier = Modifier,
    cornerRadius: Dp = 20.dp,
    elevation: Dp = 8.dp,
    backgroundColor: Color = NeuroSurfaceLight,
    shadowColor: Color = NeuroLightShadow,
    highlightColor: Color = NeuroLightHighlight,
    contentPadding: Dp = 20.dp,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(cornerRadius))
            .drawBehind {
                drawIntoCanvas { canvas ->
                    val paint = Paint()
                    val frameworkPaint = paint.asFrameworkPaint()
                    val insetDepth = elevation.toPx() * 0.5f
                    
                    // Draw inset shadow (top-left, inside)
                    frameworkPaint.color = shadowColor.toArgb()
                    frameworkPaint.setShadowLayer(
                        insetDepth,
                        -insetDepth,
                        -insetDepth,
                        shadowColor.toArgb()
                    )
                    canvas.drawRoundRect(
                        left = insetDepth,
                        top = insetDepth,
                        right = size.width - insetDepth,
                        bottom = size.height - insetDepth,
                        radiusX = cornerRadius.toPx(),
                        radiusY = cornerRadius.toPx(),
                        paint = paint
                    )
                    
                    // Draw inset highlight (bottom-right, inside)
                    frameworkPaint.color = highlightColor.toArgb()
                    frameworkPaint.setShadowLayer(
                        insetDepth,
                        insetDepth,
                        insetDepth,
                        highlightColor.toArgb()
                    )
                    canvas.drawRoundRect(
                        left = insetDepth,
                        top = insetDepth,
                        right = size.width - insetDepth,
                        bottom = size.height - insetDepth,
                        radiusX = cornerRadius.toPx(),
                        radiusY = cornerRadius.toPx(),
                        paint = paint
                    )
                }
            }
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(cornerRadius)
            )
            .padding(contentPadding)
    ) {
        content()
    }
}
