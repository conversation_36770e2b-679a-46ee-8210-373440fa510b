package com.zara.assistant.services

import android.content.Context
import android.util.Log
import com.microsoft.cognitiveservices.speech.*
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import com.zara.assistant.utils.ApiKeyManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.Future
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Azure-Only Speech-to-Text Service
 * Completely replaces Android's built-in SpeechRecognizer
 * Uses Microsoft Azure Speech Services exclusively
 */
@Singleton
class AzureOnlySTTService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val apiKeyManager: ApiKeyManager
) {
    companion object {
        private const val TAG = "AzureOnlySTT"
        private const val DEFAULT_LANGUAGE = "en-US"
    }

    interface STTListener {
        fun onReady()
        fun onListeningStarted()
        fun onPartialResult(text: String)
        fun onFinalResult(text: String)
        fun onListeningStopped()
        fun onError(error: String)
    }

    private var speechRecognizer: SpeechRecognizer? = null
    private var speechConfig: SpeechConfig? = null
    private var audioConfig: AudioConfig? = null
    private var isInitialized = false
    private var isListening = false
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private var currentListener: STTListener? = null

    private val _isListening = MutableStateFlow(false)
    val isListeningFlow: StateFlow<Boolean> = _isListening.asStateFlow()

    private val _partialText = MutableStateFlow("")
    val partialTextFlow: StateFlow<String> = _partialText.asStateFlow()

    /**
     * Initialize Azure Speech STT Service with actual API keys
     */
    fun initialize(): Boolean {
        return try {
            Log.d(TAG, "🚀 Initializing Azure-Only STT Service...")

            // Get actual API keys from assets
            val speechKey = apiKeyManager.getAzureSpeechKey()
            val speechRegion = apiKeyManager.getAzureSpeechRegion()

            Log.d(TAG, "🔑 Azure Speech Key: ${speechKey?.take(10)}...")
            Log.d(TAG, "🌍 Azure Speech Region: $speechRegion")

            if (speechKey.isNullOrEmpty() || speechKey == "placeholder" || speechKey == "your_actual_azure_speech_key_here") {
                Log.e(TAG, "❌ Azure Speech API key not configured properly: '$speechKey'")
                return false
            }

            if (speechRegion.isNullOrEmpty()) {
                Log.e(TAG, "❌ Azure Speech region not configured")
                return false
            }

            // Create speech configuration with actual keys
            Log.d(TAG, "🔧 Creating Azure Speech Config...")
            speechConfig = SpeechConfig.fromSubscription(speechKey, speechRegion).apply {
                Log.d(TAG, "📝 Setting language to: $DEFAULT_LANGUAGE")
                speechRecognitionLanguage = DEFAULT_LANGUAGE
                enableDictation()
                requestWordLevelTimestamps()

                // Optimize for real-time recognition with more patient timeouts
                Log.d(TAG, "⚙️ Setting Azure Speech properties for voice commands...")
                setProperty("SPEECH_SERVICE_CONNECTION_INITIAL_SILENCE_TIMEOUT_MS", "8000")  // 8 seconds initial
                setProperty("SPEECH_SERVICE_CONNECTION_END_SILENCE_TIMEOUT_MS", "3000")      // 3 seconds end silence
                setProperty("SPEECH_SEGMENTATION_SILENCE_TIMEOUT_MS", "1500")               // 1.5 seconds segmentation
                setProperty("SPEECH_SERVICE_CONNECTION_PROFANITY_OPTION", "Raw")

                // Additional properties for better voice command recognition
                setProperty("SPEECH_SERVICE_RESPONSE_REQUESTED_PHRASE_OUTPUT", "detailed")
                setProperty("SPEECH_SERVICE_CONNECTION_ENABLE_AUDIO_LOGGING", "false")
            }

            // Create audio configuration for microphone input
            Log.d(TAG, "🎤 Creating audio config for microphone...")
            audioConfig = AudioConfig.fromDefaultMicrophoneInput()

            isInitialized = true
            Log.d(TAG, "✅ Azure-Only STT initialized successfully!")
            true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to initialize Azure-Only STT", e)
            false
        }
    }

    /**
     * Start listening with a specific listener
     */
    fun startListening(listener: STTListener, continuous: Boolean = false): Boolean {
        Log.d(TAG, "🎤 startListening() called - initialized: $isInitialized, listening: $isListening")

        if (!isInitialized) {
            Log.w(TAG, "❌ Azure STT not initialized")
            listener.onError("STT not initialized")
            return false
        }

        if (isListening) {
            Log.w(TAG, "⚠️ Already listening - stopping previous session")
            stopListening()
        }

        currentListener = listener

        return try {
            // Always create a fresh speech recognizer for each session (Microsoft recommendation)
            Log.d(TAG, "🔄 Creating fresh Azure SpeechRecognizer instance...")
            speechRecognizer?.close() // Clean up any existing recognizer

            speechRecognizer = SpeechRecognizer(speechConfig, audioConfig)

            if (speechRecognizer == null) {
                Log.e(TAG, "❌ Failed to create SpeechRecognizer instance")
                listener.onError("Failed to create speech recognizer")
                return false
            }

            setupEventHandlers(speechRecognizer!!, listener)
            Log.d(TAG, "✅ Fresh SpeechRecognizer created and configured successfully")

            // Start recognition immediately (Microsoft recommended pattern)
            try {
                if (continuous) {
                    Log.d(TAG, "🔄 Starting continuous Azure Speech recognition...")
                    val result = speechRecognizer?.startContinuousRecognitionAsync()
                    Log.d(TAG, "🔄 Continuous recognition async result: $result")
                } else {
                    Log.d(TAG, "🎯 Starting single Azure Speech recognition...")
                    // Use recognizeOnceAsync for single recognition (Microsoft pattern)
                    val future = speechRecognizer?.recognizeOnceAsync()
                    Log.d(TAG, "🎯 Single recognition future: $future")
                }

                // Mark as listening
                isListening = true
                _isListening.value = true
                Log.d(TAG, "✅ Azure recognition started - waiting for session events...")

            } catch (e: Exception) {
                Log.e(TAG, "❌ Failed to start Azure recognition", e)
                listener.onError("Failed to start recognition: ${e.message}")
                return false
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error starting listening", e)
            listener.onError("Failed to start listening: ${e.message}")
            false
        }
    }

    /**
     * Setup event handlers for Azure Speech recognizer
     */
    private fun setupEventHandlers(recognizer: SpeechRecognizer, listener: STTListener) {
        Log.d(TAG, "🔧 Setting up Azure Speech event handlers...")
        // Session started
        recognizer.sessionStarted.addEventListener { _, e ->
            Log.d(TAG, "🟢 Azure session started: ${e.sessionId}")
            isListening = true
            _isListening.value = true
            try {
                listener.onListeningStarted()
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error calling listener.onListeningStarted()", e)
            }
        }

        // Session stopped
        recognizer.sessionStopped.addEventListener { _, e ->
            Log.d(TAG, "Azure session stopped: ${e.sessionId}")
            isListening = false
            _isListening.value = false
            try {
                listener.onListeningStopped()
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error calling listener.onListeningStopped()", e)
            }
        }

        // Speech start detected
        recognizer.speechStartDetected.addEventListener { _, e ->
            Log.d(TAG, "🎙️ Azure speech START detected at offset: ${e.offset}")
        }

        // Speech end detected
        recognizer.speechEndDetected.addEventListener { _, e ->
            Log.d(TAG, "🔇 Azure speech END detected at offset: ${e.offset}")
            Log.d(TAG, "⚠️ Speech ended - waiting for final result...")
        }

        // Recognizing (partial results)
        recognizer.recognizing.addEventListener { _, e ->
            if (e.result.reason == ResultReason.RecognizingSpeech) {
                val text = e.result.text.trim()
                if (text.isNotBlank()) {
                    Log.d(TAG, "🔄 Azure partial result: '$text'")
                    _partialText.value = text
                    try {
                        listener.onPartialResult(text)
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ Error calling listener.onPartialResult()", e)
                    }
                }
            }
        }

        // Recognized (final results)
        recognizer.recognized.addEventListener { _, e ->
            when (e.result.reason) {
                ResultReason.RecognizedSpeech -> {
                    val text = e.result.text.trim()
                    if (text.isNotBlank()) {
                        Log.d(TAG, "✅ Azure final result: '$text'")
                        _partialText.value = ""
                        Log.d(TAG, "🔄 Calling listener.onFinalResult() for: '$text'")

                        // Call directly on main thread instead of using coroutine scope
                        try {
                            Log.d(TAG, "📞 Executing listener.onFinalResult() directly")
                            listener.onFinalResult(text)
                            Log.d(TAG, "✅ listener.onFinalResult() completed successfully")
                        } catch (e: Exception) {
                            Log.e(TAG, "❌ Error calling listener.onFinalResult()", e)
                        }
                    } else {
                        Log.w(TAG, "⚠️ Azure empty recognition result")
                    }
                }
                ResultReason.NoMatch -> {
                    Log.w(TAG, "🤷 Azure: No speech could be recognized - try speaking louder or closer to microphone")
                    serviceScope.launch {
                        withContext(Dispatchers.Main) {
                            listener.onError("No speech recognized - please try again")
                        }
                    }
                }
                else -> {
                    Log.w(TAG, "Azure recognition result: ${e.result.reason}")
                }
            }
        }

        // Canceled (errors)
        recognizer.canceled.addEventListener { _, e ->
            Log.e(TAG, "Azure recognition canceled: ${e.reason}")
            isListening = false
            _isListening.value = false
            
            val errorMessage = when (e.reason) {
                CancellationReason.Error -> {
                    Log.e(TAG, "Azure error details: ${e.errorDetails}")
                    "Azure Speech error: ${e.errorDetails}"
                }
                CancellationReason.EndOfStream -> "End of audio stream"
                else -> "Azure recognition canceled: ${e.reason}"
            }
            
            serviceScope.launch {
                withContext(Dispatchers.Main) {
                    listener.onError(errorMessage)
                }
            }
        }
    }

    /**
     * Stop listening
     */
    fun stopListening() {
        if (!isListening) {
            Log.d(TAG, "🔇 stopListening() called but not currently listening")
            return
        }

        Log.d(TAG, "🛑 Stopping Azure STT listening...")
        serviceScope.launch {
            try {
                speechRecognizer?.let { recognizer ->
                    if (isListening) {
                        Log.d(TAG, "🔄 Stopping continuous recognition...")
                        recognizer.stopContinuousRecognitionAsync()
                    }
                    // Don't close here - will be closed when creating new instance
                }
                isListening = false
                _isListening.value = false
                _partialText.value = ""
                Log.d(TAG, "✅ Azure STT listening stopped successfully")
            } catch (e: Exception) {
                Log.e(TAG, "❌ Failed to stop Azure listening", e)
            }
        }
    }

    /**
     * Cancel current recognition
     */
    fun cancel() {
        Log.d(TAG, "🚫 Cancelling Azure STT recognition...")
        serviceScope.launch {
            try {
                speechRecognizer?.let { recognizer ->
                    Log.d(TAG, "🔄 Stopping and closing recognizer...")
                    recognizer.stopContinuousRecognitionAsync()
                    recognizer.close()
                }
                speechRecognizer = null
                isListening = false
                _isListening.value = false
                _partialText.value = ""
                Log.d(TAG, "✅ Azure STT cancelled successfully")
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error cancelling Azure STT", e)
            }
        }
    }

    /**
     * Cleanup resources
     */
    fun destroy() {
        try {
            Log.d(TAG, "🧹 Destroying Azure-Only STT Service...")

            // First, stop listening if active
            if (isListening) {
                Log.d(TAG, "🔄 Stopping active recognition before destroy...")
                stopListening()
                // Give it a moment to stop gracefully
                Thread.sleep(100)
            }

            // Cancel any ongoing operations
            serviceScope.cancel()

            // Now safely cleanup recognizer
            speechRecognizer?.let { recognizer ->
                try {
                    Log.d(TAG, "🔄 Closing speech recognizer...")
                    recognizer.close()
                } catch (e: IllegalStateException) {
                    Log.w(TAG, "⚠️ Recognizer was already disposed or still running: ${e.message}")
                } catch (e: Exception) {
                    Log.e(TAG, "❌ Error closing recognizer", e)
                }
            }
            speechRecognizer = null

            // Cleanup configurations
            Log.d(TAG, "🔄 Cleaning up audio and speech configs...")
            try {
                audioConfig?.close()
            } catch (e: Exception) {
                Log.w(TAG, "⚠️ Error closing audio config", e)
            }
            audioConfig = null

            try {
                speechConfig?.close()
            } catch (e: Exception) {
                Log.w(TAG, "⚠️ Error closing speech config", e)
            }
            speechConfig = null

            isInitialized = false
            isListening = false
            currentListener = null

            Log.d(TAG, "✅ Azure-Only STT Service destroyed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error during Azure STT cleanup", e)
        }
    }

    /**
     * Check if currently listening
     */
    fun isListening(): Boolean = isListening

    /**
     * Check if initialized
     */
    fun isInitialized(): Boolean = isInitialized

    /**
     * Check if Azure Speech is available
     */
    fun isAvailable(): Boolean {
        return isInitialized && !apiKeyManager.getAzureSpeechKey().isNullOrEmpty()
    }

    /**
     * Set recognition language
     */
    fun setLanguage(language: String) {
        speechConfig?.speechRecognitionLanguage = language
        Log.d(TAG, "Azure language set to: $language")
    }

    /**
     * Enable/disable profanity filtering
     */
    fun setProfanityFilter(enabled: Boolean) {
        speechConfig?.setProperty(
            "SPEECH_SERVICE_CONNECTION_PROFANITY_OPTION",
            if (enabled) "Masked" else "Raw"
        )
        Log.d(TAG, "Azure profanity filter: ${if (enabled) "enabled" else "disabled"}")
    }
}
