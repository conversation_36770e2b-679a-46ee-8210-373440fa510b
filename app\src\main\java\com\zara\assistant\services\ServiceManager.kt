package com.zara.assistant.services

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.zara.assistant.utils.ApiKeyManager
import com.zara.assistant.utils.PermissionManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages the lifecycle and dependencies of all Zara services
 */
@Singleton
class ServiceManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val apiKeyManager: ApiKeyManager,
    private val permissionManager: PermissionManager
) : DefaultLifecycleObserver {

    companion object {
        private const val TAG = "ServiceManager"
    }

    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private var isInitialized = false
    private var servicesStarted = false

    // Service states with enhanced tracking
    private var wakeWordServiceRunning = false
    private var voiceProcessingServiceRunning = false
    private var aiOrchestrationInitialized = false

    // Error tracking and recovery
    private val serviceErrors = mutableMapOf<String, ServiceError>()
    private val retryAttempts = mutableMapOf<String, Int>()
    private val maxRetryAttempts = 3
    private val retryDelayMs = 2000L

    data class ServiceStatus(
        val serviceName: String,
        val isRunning: Boolean,
        val isRequired: Boolean,
        val dependsOn: List<String> = emptyList(),
        val errorMessage: String? = null,
        val lastError: ServiceError? = null,
        val retryCount: Int = 0
    )

    data class ServiceError(
        val timestamp: Long,
        val exception: Throwable,
        val message: String,
        val isRecoverable: Boolean = true
    )

    init {
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
    }

    /**
     * Initialize the service manager
     */
    suspend fun initialize() {
        if (isInitialized) return

        try {
            Log.d(TAG, "🚀 Initializing Service Manager...")

            // Check prerequisites
            if (!apiKeyManager.isInitialized()) {
                Log.w(TAG, "⚠️ API Key Manager not initialized")
            }

            // Initialize core services in order
            initializeCoreServices()

            isInitialized = true
            Log.d(TAG, "✅ Service Manager initialized successfully")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error initializing Service Manager", e)
            throw e
        }
    }

    /**
     * Start all services based on permissions and configuration
     */
    suspend fun startServices() {
        if (!isInitialized) {
            initialize()
        }

        if (servicesStarted) {
            Log.d(TAG, "Services already started")
            return
        }

        try {
            Log.d(TAG, "🚀 Starting Zara services...")

            // Check core permissions
            if (!permissionManager.areCorePermissionsGranted()) {
                Log.w(TAG, "⚠️ Core permissions not granted, some services will be limited")
            }

            // Start services in dependency order
            startWakeWordService()
            startVoiceProcessingService()
            startAIServices()

            servicesStarted = true
            Log.d(TAG, "✅ All services started successfully")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error starting services", e)
            throw e
        }
    }

    /**
     * Stop all services
     */
    suspend fun stopServices() {
        try {
            Log.d(TAG, "🛑 Stopping Zara services...")

            // Stop services in reverse order
            stopAIServices()
            stopVoiceProcessingService()
            stopWakeWordService()

            servicesStarted = false
            Log.d(TAG, "✅ All services stopped successfully")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error stopping services", e)
        }
    }

    /**
     * Get status of all services
     */
    fun getServiceStatuses(): List<ServiceStatus> {
        return listOf(
            ServiceStatus(
                serviceName = "Wake Word Detection",
                isRunning = wakeWordServiceRunning,
                isRequired = true,
                dependsOn = listOf("Microphone Permission", "Porcupine API Key")
            ),
            ServiceStatus(
                serviceName = "Voice Processing",
                isRunning = voiceProcessingServiceRunning,
                isRequired = true,
                dependsOn = listOf("Wake Word Detection", "Azure Speech API")
            ),
            ServiceStatus(
                serviceName = "AI Orchestration",
                isRunning = aiOrchestrationInitialized,
                isRequired = true,
                dependsOn = listOf("Cohere API", "Perplexity API")
            )
        )
    }



    private suspend fun initializeCoreServices() {
        // Initialize AI Orchestration Service
        try {
            // This would initialize the AI orchestration service
            // For now, we'll just mark it as initialized
            aiOrchestrationInitialized = true
            Log.d(TAG, "✅ AI Orchestration Service initialized")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to initialize AI Orchestration Service", e)
            aiOrchestrationInitialized = false
            handleServiceError("AIOrchestrationService", e)
        }
    }

    private suspend fun startWakeWordService() {
        try {
            // Check prerequisites
            if (!permissionManager.areCorePermissionsGranted()) {
                Log.w(TAG, "Cannot start Wake Word Service - missing microphone permission")
                return
            }

            if (apiKeyManager.getPorcupineAccessKey().isEmpty()) {
                Log.w(TAG, "Cannot start Wake Word Service - missing Porcupine API key")
                return
            }

            // Start the service
            val intent = Intent(context, WakeWordService::class.java).apply {
                action = WakeWordService.ACTION_START_DETECTION
            }
            context.startForegroundService(intent)

            wakeWordServiceRunning = true
            Log.d(TAG, "✅ Wake Word Service started")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start Wake Word Service", e)
            wakeWordServiceRunning = false
            handleServiceError("WakeWordService", e)
        }
    }

    private suspend fun stopWakeWordService() {
        try {
            val intent = Intent(context, WakeWordService::class.java).apply {
                action = WakeWordService.ACTION_STOP_DETECTION
            }
            context.startService(intent)

            wakeWordServiceRunning = false
            Log.d(TAG, "✅ Wake Word Service stopped")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop Wake Word Service", e)
        }
    }

    private suspend fun startVoiceProcessingService() {
        try {
            // Check prerequisites
            if (!wakeWordServiceRunning) {
                Log.w(TAG, "Cannot start Voice Processing Service - Wake Word Service not running")
                return
            }

            if (apiKeyManager.getAzureSpeechKey().isEmpty()) {
                Log.w(TAG, "Cannot start Voice Processing Service - missing Azure Speech API key")
                return
            }

            // Start the service
            val intent = Intent(context, AdvancedVoiceProcessingService::class.java)
            context.startForegroundService(intent)

            voiceProcessingServiceRunning = true
            Log.d(TAG, "✅ Voice Processing Service started")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start Voice Processing Service", e)
            voiceProcessingServiceRunning = false
            handleServiceError("VoiceProcessingService", e)
        }
    }

    private suspend fun stopVoiceProcessingService() {
        try {
            val intent = Intent(context, AdvancedVoiceProcessingService::class.java)
            context.stopService(intent)

            voiceProcessingServiceRunning = false
            Log.d(TAG, "✅ Voice Processing Service stopped")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop Voice Processing Service", e)
        }
    }

    private suspend fun startAIServices() {
        try {
            // Check prerequisites
            val hasAnyAIKey = apiKeyManager.getCohereApiKey().isNotEmpty() ||
                    apiKeyManager.getPerplexityApiKey().isNotEmpty()

            if (!hasAnyAIKey) {
                Log.w(TAG, "Cannot start AI Services - missing AI API keys")
                return
            }

            // AI services are typically not foreground services
            // They're initialized as singletons through dependency injection
            aiOrchestrationInitialized = true
            Log.d(TAG, "✅ AI Services started")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start AI Services", e)
            aiOrchestrationInitialized = false
        }
    }

    private suspend fun stopAIServices() {
        try {
            // AI services cleanup would happen here
            aiOrchestrationInitialized = false
            Log.d(TAG, "✅ AI Services stopped")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop AI Services", e)
        }
    }

    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        serviceScope.launch {
            try {
                startServices()
            } catch (e: Exception) {
                Log.e(TAG, "Error starting services on app start", e)
            }
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        serviceScope.launch {
            try {
                // Don't stop services when app goes to background
                // They should continue running as foreground services
                Log.d(TAG, "App backgrounded - services continue running")
            } catch (e: Exception) {
                Log.e(TAG, "Error handling app background", e)
            }
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        serviceScope.launch {
            try {
                stopServices()
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping services on app destroy", e)
            }
        }
        serviceScope.cancel()
    }

    /**
     * Enhanced error handling and recovery
     */
    private fun handleServiceError(serviceName: String, error: Throwable, isRecoverable: Boolean = true) {
        val serviceError = ServiceError(
            timestamp = System.currentTimeMillis(),
            exception = error,
            message = error.message ?: "Unknown error",
            isRecoverable = isRecoverable
        )

        serviceErrors[serviceName] = serviceError
        Log.e(TAG, "Service error in $serviceName: ${error.message}", error)

        if (isRecoverable) {
            scheduleServiceRetry(serviceName)
        } else {
            Log.e(TAG, "Non-recoverable error in $serviceName - manual intervention required")
        }
    }

    private fun scheduleServiceRetry(serviceName: String) {
        val currentRetries = retryAttempts.getOrDefault(serviceName, 0)

        if (currentRetries < maxRetryAttempts) {
            retryAttempts[serviceName] = currentRetries + 1
            Log.i(TAG, "Scheduling retry for $serviceName (attempt ${currentRetries + 1}/$maxRetryAttempts)")

            serviceScope.launch {
                delay(retryDelayMs * (currentRetries + 1)) // Exponential backoff
                retryService(serviceName)
            }
        } else {
            Log.e(TAG, "Max retry attempts reached for $serviceName - giving up")
        }
    }

    private suspend fun retryService(serviceName: String) {
        try {
            Log.i(TAG, "Retrying service: $serviceName")

            when (serviceName) {
                "WakeWordService" -> {
                    if (permissionManager.areCorePermissionsGranted()) {
                        startWakeWordService()
                    }
                }
                "AdvancedVoiceProcessingService" -> {
                    if (permissionManager.areCorePermissionsGranted()) {
                        startVoiceProcessingService()
                    }
                }
                "AIOrchestrationService" -> {
                    initializeCoreServices()
                }
            }

            // Clear error if retry successful
            serviceErrors.remove(serviceName)
            retryAttempts.remove(serviceName)
            Log.i(TAG, "Successfully retried service: $serviceName")

        } catch (e: Exception) {
            Log.e(TAG, "Retry failed for $serviceName", e)
            handleServiceError(serviceName, e)
        }
    }

    /**
     * Get detailed service health status
     */
    fun getServiceHealthStatus(): Map<String, ServiceStatus> {
        return mapOf(
            "WakeWordService" to ServiceStatus(
                serviceName = "WakeWordService",
                isRunning = wakeWordServiceRunning,
                isRequired = true,
                dependsOn = listOf("RecordAudioPermission"),
                errorMessage = serviceErrors["WakeWordService"]?.message,
                lastError = serviceErrors["WakeWordService"],
                retryCount = retryAttempts.getOrDefault("WakeWordService", 0)
            ),
            "VoiceProcessingService" to ServiceStatus(
                serviceName = "VoiceProcessingService",
                isRunning = voiceProcessingServiceRunning,
                isRequired = true,
                dependsOn = listOf("RecordAudioPermission", "AzureSTT"),
                errorMessage = serviceErrors["VoiceProcessingService"]?.message,
                lastError = serviceErrors["VoiceProcessingService"],
                retryCount = retryAttempts.getOrDefault("VoiceProcessingService", 0)
            ),
            "AIOrchestrationService" to ServiceStatus(
                serviceName = "AIOrchestrationService",
                isRunning = aiOrchestrationInitialized,
                isRequired = true,
                dependsOn = listOf("APIKeys", "MLModels"),
                errorMessage = serviceErrors["AIOrchestrationService"]?.message,
                lastError = serviceErrors["AIOrchestrationService"],
                retryCount = retryAttempts.getOrDefault("AIOrchestrationService", 0)
            )
        )
    }

    /**
     * Force restart a specific service
     */
    suspend fun restartService(serviceName: String) {
        try {
            Log.i(TAG, "Force restarting service: $serviceName")

            // Clear previous errors
            serviceErrors.remove(serviceName)
            retryAttempts.remove(serviceName)

            when (serviceName) {
                "WakeWordService" -> {
                    stopWakeWordService()
                    delay(500) // Give it time to stop
                    startWakeWordService()
                }
                "VoiceProcessingService" -> {
                    stopVoiceProcessingService()
                    delay(500)
                    startVoiceProcessingService()
                }
                "AIOrchestrationService" -> {
                    // AI Orchestration doesn't need stopping, just reinitialize
                    initializeCoreServices()
                }
            }

            Log.i(TAG, "Successfully restarted service: $serviceName")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to restart service: $serviceName", e)
            handleServiceError(serviceName, e)
        }
    }

    /**
     * Clear all service errors (for manual recovery)
     */
    fun clearServiceErrors() {
        serviceErrors.clear()
        retryAttempts.clear()
        Log.i(TAG, "Cleared all service errors")
    }
}
